set -e
sed -i "/AppVersionDate\|AppVersionTime/d" global/version.go
date=$(date "+%Y-%m-%d")
sed -i "/AppVersionCode/aconst AppVersionDate = \"${date}\"" global/version.go
time=$(date "+%H:%M:%S")
sed -i "/AppVersionDate/aconst AppVersionTime = \"${time}\"" global/version.go
expect deploy/build_from.exp 192.168.9.167 root ylzl@2018
app_version=$(egrep -o "[0-9]{1,}\.[0-9]{1,}\.[0-9]{1,}" global/version.go)
cp deploy/build_from.out //192.168.9.161/share/ylzl/dists/前置系统/tfai-front-arm64-${app_version}
echo "package finished"
