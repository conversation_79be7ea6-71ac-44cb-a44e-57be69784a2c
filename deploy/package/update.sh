#!/bin/bash

if [ ! -f "$1" ]; then
  exit 1
else
  echo "found update file $1, begin update"
fi
echo "removing temp directory"
rm -rf temp
echo "unzipping update file $1 to temp directory"
unzip -O CP936 "$1" -d temp
# mkdir temp
# tar -xf "$1" -C temp
exit_code=$?
if [ ${exit_code} != 0 ]; then 
  echo "unzip update file $1 failed with exit code ${exit_code}"
  exit 2
else
  if [ -f "temp/stvpp" ]; then
    mv stvpp stvpp-$(date "+%Y%m%d%H%M%S")
    mv temp/stvpp stvpp
	exit_code=$?
    if [ ${exit_code} != 0 ]; then 
      echo "move temp/stvpp to stvpp failed with exit code ${exit_code}"
      exit 3
    fi
    chmod +x stvpp
  fi
  if [ -d "temp/html" ]; then
    mv html html-$(date "+%Y%m%d%H%M%S")
    mv temp/html html
	exit_code=$?
    if [ ${exit_code} != 0 ]; then 
      echo "move temp/html to html failed with exit code ${exit_code}"
      exit 4
    fi
  fi
  if [ -d "temp/asset" ]; then
    cp -rf temp/asset/* asset
	exit_code=$?
    if [ ${exit_code} != 0 ]; then 
      echo "copy temp/asset/* to asset failed with exit code ${exit_code}"
      exit 5
    fi
  fi
  if [ -f "temp/start.sh" ]; then
    mv start.sh start.sh-$(date "+%Y%m%d%H%M%S")
    mv temp/start.sh start.sh
	exit_code=$?
    if [ ${exit_code} != 0 ]; then 
      echo "move temp/start.sh to start.sh failed with exit code ${exit_code}"
      exit 6
    fi
	chmod +x start.sh
  fi
  if [ -f "temp/stop.sh" ]; then
    mv stop.sh stop.sh-$(date "+%Y%m%d%H%M%S")
    mv temp/stop.sh stop.sh
	exit_code=$?
    if [ ${exit_code} != 0 ]; then 
      echo "move temp/stop.sh to stop.sh failed with exit code ${exit_code}"
      exit 7
    fi
	chmod +x stop.sh
  fi
    if [ -f "temp/update.sh" ]; then
    mv update.sh update.sh-$(date "+%Y%m%d%H%M%S")
    mv temp/update.sh update.sh
	exit_code=$?
    if [ ${exit_code} != 0 ]; then 
      echo "move temp/update.sh to update.sh failed with exit code ${exit_code}"
      exit 8
    fi
	chmod +x update.sh
  fi
  if [ -f "temp/watch.cfg" ]; then
    mv watch.cfg watch.cfg-$(date "+%Y%m%d%H%M%S")
    mv temp/watch.cfg watch.cfg
	exit_code=$?
    if [ ${exit_code} != 0 ]; then 
      echo "move temp/watch.cfg to watch.cfg failed with exit code ${exit_code}"
      exit 9
    fi
  fi
fi

