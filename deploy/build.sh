#!/bin/bash

# Go项目打包部署脚本
# 用法: ./deploy/build.sh <host> <username> <password> [target_dir]

# 参数检查
if [ $# -lt 3 ]; then
    echo "用法: $0 <host> <username> <password> [target_dir]"
    exit 1
fi

HOST=$1
USERNAME=$2
PASSWORD=$3
PROJECT_NAME=$4
TARGET_DIR="/opt/tfai/"$PROJECT_NAME

# 定义项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
cd "$PROJECT_ROOT" || exit 1

# 清理函数
cleanup() {
    rm -f "$PROJECT_NAME"
}
trap cleanup EXIT

echo "开始打包部署..."

# 准备Go模块
go mod tidy
go mod vendor -v

# 编译项目
go build -o "$PROJECT_NAME" main.go
echo "编译完成"

# 获取版本信息
VERSION_FILE="global/version.go"
app_version=$(grep -o "[0-9]\{1,\}\.[0-9]\{1,\}\.[0-9]\{1,\}" "$VERSION_FILE" | head -1)

# 暂停服务监控
curl -s "http://$HOST:9527/appPauseWatch?appId=$PROJECT_NAME" > /dev/null 2>&1

# 停止远程服务
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$HOST" "cd $TARGET_DIR && [ -f stop.sh ] && sh ./stop.sh" 2>/dev/null

# 创建目标目录
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$HOST" "mkdir -p $TARGET_DIR"

# 上传二进制文件
sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no "$PROJECT_NAME" "$USERNAME@$HOST:$TARGET_DIR/$PROJECT_NAME"
echo "二进制文件上传完成"

# 上传配置文件和脚本
[ -d "deploy/package/conf" ] && sshpass -p "$PASSWORD" scp -r -o StrictHostKeyChecking=no deploy/package/conf/* "$USERNAME@$HOST:$TARGET_DIR/conf/"
[ -f "deploy/package/start.sh" ] && sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no deploy/package/start.sh "$USERNAME@$HOST:$TARGET_DIR/start.sh"
[ -f "deploy/package/stop.sh" ] && sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no deploy/package/stop.sh "$USERNAME@$HOST:$TARGET_DIR/stop.sh"
[ -f "deploy/package/update.sh" ] && sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no deploy/package/update.sh "$USERNAME@$HOST:$TARGET_DIR/update.sh"
[ -f "deploy/package/watch.cfg" ] && sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no deploy/package/watch.cfg "$USERNAME@$HOST:$TARGET_DIR/watch.cfg"
echo "配置文件上传完成"

# 设置文件权限
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$HOST" "cd $TARGET_DIR && chmod +x $PROJECT_NAME start.sh stop.sh update.sh"

# 启动服务
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$USERNAME@$HOST" "cd $TARGET_DIR && sh ./start.sh"
echo "服务启动完成"

# 恢复服务监控
sleep 2
curl -s "http://$HOST:9527/appResumeWatch?appId=$PROJECT_NAME" > /dev/null 2>&1

echo "部署完成 - 版本: $app_version - 时间: $(date '+%Y-%m-%d %H:%M:%S')"

exit 0
