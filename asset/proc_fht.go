package ivv

import (
	"fmt"
	"gosmart/apps/data"
	"gosmart/engine/gocv"
	"gosmart/util/fs"
	"gosmart/util/json"
	"image"
	"image/color"
	"net"
	"os"
	"path"
	"sync"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/sirupsen/logrus"
	// "gosmart/util/conv"
	// "time"
)

// 预定义的RGB颜色表，包含122种常用颜色
var colorTable = []color.RGBA{
	// 基础颜色 (16)
	{255, 0, 0, 255},     // 红色
	{0, 255, 0, 255},     // 绿色
	{0, 0, 255, 255},     // 蓝色
	{255, 255, 0, 255},   // 黄色
	{255, 0, 255, 255},   // 紫色
	{0, 255, 255, 255},   // 青色
	{255, 128, 0, 255},   // 橙色
	{128, 0, 128, 255},   // 紫罗兰
	{0, 128, 0, 255},     // 深绿
	{128, 128, 0, 255},   // 橄榄
	{0, 0, 128, 255},     // 海军蓝
	{128, 128, 128, 255}, // 灰色
	{192, 192, 192, 255}, // 银灰
	{255, 192, 203, 255}, // 粉红
	{165, 42, 42, 255},   // 棕色
	{0, 0, 0, 255},       // 黑色

	// 红色系 (18)
	{128, 0, 0, 255},     // 深红
	{139, 0, 0, 255},     // 栗色
	{178, 34, 34, 255},   // 褐红
	{220, 20, 60, 255},   // 猩红
	{205, 92, 92, 255},   // 印第安红
	{240, 128, 128, 255}, // 浅珊瑚红
	{188, 143, 143, 255}, // 粉玫瑰色
	{255, 99, 71, 255},   // 番茄红
	{255, 69, 0, 255},    // 橙红
	{255, 140, 0, 255},   // 深橙
	{255, 160, 122, 255}, // 浅橙红
	{250, 128, 114, 255}, // 三文鱼
	{233, 150, 122, 255}, // 深橙红
	{255, 218, 185, 255}, // 桃红
	{255, 228, 225, 255}, // 雾玫红
	{255, 182, 193, 255}, // 浅粉
	{255, 105, 180, 255}, // 热粉红
	{219, 112, 147, 255}, // 粉紫红

	// 绿色系 (20)
	{0, 100, 0, 255},     // 暗绿
	{85, 107, 47, 255},   // 暗橄榄绿
	{107, 142, 35, 255},  // 橄榄绿
	{127, 255, 0, 255},   // 绿黄色
	{173, 255, 47, 255},  // 绿黄
	{50, 205, 50, 255},   // 酸橙绿
	{152, 251, 152, 255}, // 浅绿
	{144, 238, 144, 255}, // 亮绿
	{60, 179, 113, 255},  // 中海绿
	{46, 139, 87, 255},   // 海绿
	{34, 139, 34, 255},   // 森林绿
	{0, 255, 127, 255},   // 春绿
	{0, 250, 154, 255},   // 中海春绿
	{102, 205, 170, 255}, // 中青绿色
	{32, 178, 170, 255},  // 浅海绿
	{64, 224, 208, 255},  // 绿松石
	{0, 206, 209, 255},   // 暗绿松石
	{95, 158, 160, 255},  // 鼠尾草绿
	{72, 209, 204, 255},  // 中绿松石
	{47, 79, 79, 255},    // 深石板灰

	// 蓝色系 (20)
	{0, 0, 128, 255},     // 海军蓝
	{25, 25, 112, 255},   // 午夜蓝
	{0, 0, 205, 255},     // 中蓝
	{65, 105, 225, 255},  // 皇室蓝
	{70, 130, 180, 255},  // 钢蓝
	{100, 149, 237, 255}, // 矢车菊蓝
	{30, 144, 255, 255},  // 道奇蓝
	{135, 206, 235, 255}, // 浅天蓝
	{135, 206, 250, 255}, // 浅蓝
	{176, 196, 222, 255}, // 浅钢蓝
	{173, 216, 230, 255}, // 淡蓝
	{176, 224, 230, 255}, // 粉蓝
	{175, 238, 238, 255}, // 浅青色
	{0, 191, 255, 255},   // 深天蓝
	{95, 158, 160, 255},  // 石板灰蓝
	{123, 104, 238, 255}, // 中紫罗兰
	{106, 90, 205, 255},  // 石板蓝
	{72, 61, 139, 255},   // 暗石板蓝
	{147, 112, 219, 255}, // 中兰花紫
	{139, 0, 139, 255},   // 深紫红

	// 紫色系 (15)
	{128, 0, 128, 255},   // 紫
	{216, 191, 216, 255}, // 蓟色
	{221, 160, 221, 255}, // 梅色
	{238, 130, 238, 255}, // 紫罗兰
	{218, 112, 214, 255}, // 兰花紫
	{186, 85, 211, 255},  // 中兰花紫
	{153, 50, 204, 255},  // 暗紫罗兰
	{138, 43, 226, 255},  // 蓝紫
	{148, 0, 211, 255},   // 暗紫罗兰
	{139, 0, 139, 255},   // 深洋红
	{199, 21, 133, 255},  // 洋红
	{208, 32, 144, 255},  // 紫红
	{219, 112, 147, 255}, // 淡紫红
	{255, 20, 147, 255},  // 深粉
	{255, 0, 255, 255},   // 洋红

	// 黄色/橙色系 (14)
	{128, 128, 0, 255},   // 橄榄
	{189, 183, 107, 255}, // 暗卡其色
	{240, 230, 140, 255}, // 卡其色
	{245, 222, 179, 255}, // 小麦色
	{255, 215, 0, 255},   // 金
	{255, 250, 205, 255}, // 柠檬绸
	{250, 250, 210, 255}, // 浅黄
	{255, 255, 224, 255}, // 浅金
	{139, 69, 19, 255},   // 马鞍棕
	{160, 82, 45, 255},   // 赭色
	{205, 133, 63, 255},  // 黄褐色
	{222, 184, 135, 255}, // 古铜色
	{210, 180, 140, 255}, // 棕褐色
	{244, 164, 96, 255},  // 沙棕色

	// 灰色/棕色系 (19)
	{105, 105, 105, 255}, // 暗灰
	{169, 169, 169, 255}, // 暗灰
	{119, 136, 153, 255}, // 浅石板灰
	{112, 128, 144, 255}, // 石板灰
	{192, 192, 192, 255}, // 银灰
	{211, 211, 211, 255}, // 浅灰
	{220, 220, 220, 255}, // 亮灰
	{245, 245, 245, 255}, // 烟白
	{250, 250, 250, 255}, // 亮白
	{255, 250, 240, 255}, // 亚麻色
	{253, 245, 230, 255}, // 旧蕾丝
	{245, 245, 220, 255}, // 米色
	{255, 239, 213, 255}, // 杏仁白
	{255, 235, 205, 255}, // 骨色
	{255, 228, 196, 255}, // 桃木色
	{210, 180, 140, 255}, // 棕褐色
	{188, 143, 143, 255}, // 粉玫瑰棕
	{205, 133, 63, 255},  // 秘鲁棕
	{139, 115, 85, 255},  // 中棕
}

const (
	ObjUnknow   = 0
	ObjMan      = 1
	ObjMotor    = 2
	ObjTricycle = 3
	ObjVehicle  = 4
)

var (
	_mq        *nats.Conn
	_localIp   string
	_localPort int
	_imgPath   string
	_startTime int64
	_procMap   sync.Map
)

type TrackerProc struct {
	tracker   *BYTETracker
	vio       *gocv.VideoCapture
	zones     []TrackZone
	roi       image.Rectangle
	cid       string
	logId     string
	imgUrl    string
	imgSize   image.Point
	imgTime   int64
	curSeq    int64
	sendSeq   int64
	sendQ     sync.Map
	startTime int64
	terminate bool
	started   bool
	pause     bool
	focus     bool
}

const (
	CODE_SUCCESS                 string = "0"
	CODE_PARAM_ERROR             string = "-100"
	CODE_TRACK_NOT_EXIST         string = "-1000"
	CODE_TRACK_ALREADY_EXIST     string = "-1001"
	CODE_TRACK_ALREADY_STARTED   string = "-1002"
	CODE_TRACK_ZONE_NOT_EXIST    string = "-1003"
	CODE_TRACK_VIDEO_OPEN_FAILED string = "-1004"
	CODE_TRACK_VIDEO_READ_FAILED string = "-1005"
	CODE_TRACK_VIDEO_FINISH      string = "-1006"
)

const (
	MQ_MSG_HEARTBEAT    string = "msg_heartbeat"    // 心跳（应用端/算法端/预警端）
	MQ_MSG_TRACK_ADD    string = "msg_track_add"    // 新增视频追踪，文件或视频流（应用端->算法端）
	MQ_MSG_TRACK_DEL    string = "msg_track_del"    // 删除视频追踪，文件或视频流（应用端->算法端）
	MQ_MSG_TRACK_SNAP   string = "msg_track_snap"   // 视频追踪截图，文件或视频流（应用端->算法端）
	MQ_MSG_TRACK_CFG    string = "msg_track_cfg"    // 配置视频追踪参数（应用端->算法端）
	MQ_MSG_TRACK_START  string = "msg_track_start"  // 开始视频追踪（应用端->算法端）
	MQ_MSG_TRACK_STOP   string = "msg_track_stop"   // 结束视频追踪（应用端->算法端）
	MQ_MSG_TRACK_FOCUS  string = "msg_track_focus"  // 聚焦视频追踪，聚焦之后每帧图像都会进行算法绘制，其他视频会取消聚焦状态（应用端->算法端）
	MQ_MSG_TRACK_PAUSE  string = "msg_track_pause"  // 暂停视频追踪，仅用于视频文件（应用端->算法端）
	MQ_MSG_TRACK_RESUME string = "msg_track_resume" // 恢复视频追踪，仅用于视频文件（应用端->算法端）
	MQ_MSG_TRACK_RESET  string = "msg_track_reset"  // 清除所有任务（应用端->算法端）
	MQ_MSG_TRACK_SYNC   string = "msg_track_sync"   // 同步所有任务（应用端->算法端）
	MQ_MSG_TRACK_DATA   string = "msg_track_data"   // 发布视频追踪数据（算法端->预警端）
	MQ_MSG_TRACK_STAT   string = "msg_track_stat"   // 发布视频追踪状态，如网络异常、内部错误导致的追踪停止等（算法端->应用端）
)

type MsgHeartbeat struct {
	SIP       string `json:"sip"`       // 本端IP
	Service   string `json:"service"`   // 本端服务：算法服务填SF、预警服务填YJ
	StartTime int64  `json:"starttime"` // 启动时间戳（毫秒）
	SysTime   int64  `json:"systime"`   // 系统时间戳（毫秒）
}

type MsgTrackAdd struct {
	CID           string `json:"cid"`             // 视频唯一ID，如摄像机国标ID、文件测试ID
	SIP           string `json:"sip"`             // 发送端IP地址
	RIP           string `json:"rip"`             // 接收端IP地址
	LocalFilePath string `json:"local_file_path"` // 本地文件地址，用于测试
	streamUrl     string `json:"stream_url"`      // 视频流地址，支持RTSP/RTMP
}

type MsgTrackDel struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

type MsgTrackSnap struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

type MsgTrackCfg struct {
	CID       string      `json:"cid"`
	SIP       string      `json:"sip"`
	RIP       string      `json:"rip"`
	ImageSize []int       `json:"imagesize"` // 图像宽高[width,height]
	Zones     []TrackZone `json:"zones"`     // 人工标注区域，必须标注
}
type TrackZone struct {
	ZID       string        `json:"zid"`       // 区域唯一ID
	Direction int           `json:"direction"` // 车流方向，0不限制 1单向下 -1单向上
	XYXY      []float32     `json:"xyxy"`      // 区域顶点坐标（相对图像宽高转换成百分比）
	pts       []image.Point // 内部用，忽略
}

type MsgTrackStart struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

type MsgTrackStop struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

type MsgTrackFocus struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

type MsgTrackPause struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

type MsgTrackResume struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

type MsgTrackReset struct {
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

type MsgTrackSync struct {
	MID string `json:"mid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

type MsgTrackData struct {
	CID    string    `json:"cid"`
	T      int64     `json:"t"`       // 跟踪时间戳（毫秒）
	L      int       `json:"l"`       // 信号灯状态：0未知，1红灯，2绿灯
	ImgUrl string    `json:"img_url"` // 图片URL
	Objs   []ObjData `json:"objs"`    // 所有跟踪目标
}

type ObjData struct {
	Obj string `json:"obj"` // 对象类型：0未知，1汽车，2摩托车，3三轮车，4行人
	TID string `json:"tid"` // 轨迹ID
	ZID string `json:"zid"` // 区域ID
	X   int    `json:"x"`   // x坐标
	Y   int    `json:"y"`   // y坐标
	Ext string `json:"ext"` // 额外属性，如车牌、颜色等
}

type MsgTrackStat struct {
	CID  string `json:"cid"`
	Code string `json:"code"` // 状态代码
	Msg  string `json:"msg"`  // 状态信息
	Data string `json:"data"` // 按实际需要扩展，内容为json字符串
}

type MsgTrackResp struct {
	Code string `json:"code"` // 状态代码
	Msg  string `json:"msg"`  // 状态信息
	Data string `json:"data"` // 按实际需要扩展，内容为json字符串
}

func InitFHT(port int) {
	mqconf := "../data/mq.conf"
	if !fs.HasFile(mqconf) {
		return
	}

	fb, err := os.ReadFile(mqconf)
	if err != nil {
		logrus.Info("read mq config file error")
		return
	}

	logrus.Infof("mq config: %s", fb)

	opts := []nats.Option{
		nats.MaxReconnects(-1),
		nats.ReconnectWait(2 * time.Second),
		nats.RetryOnFailedConnect(true),

		nats.DisconnectErrHandler(func(c *nats.Conn, err error) {
			logrus.Infof("mq disconnect err: %v", err)
		}),
		nats.ReconnectHandler(func(c *nats.Conn) {
			logrus.Infof("mq reconnected")
		}),
		nats.ClosedHandler(func(c *nats.Conn) {
			logrus.Infof("mq closed")
		}),
		nats.ErrorHandler(func(c *nats.Conn, s *nats.Subscription, err error) {
			if s != nil {
				logrus.Infof("mq[%s] err: %v", s.Subject, err)
			} else {
				logrus.Infof("mq[nil] err: %v", err)
			}
		}),
	}

	_mq, err = nats.Connect(string(fb), opts...) // nats://*************:4222
	if err != nil {
		logrus.Infof("mq connect failed: %s", err.Error())
		return
	}
	_mq.Subscribe(MQ_MSG_TRACK_ADD, onTrackAdd)
	_mq.Subscribe(MQ_MSG_TRACK_DEL, onTrackDel)
	_mq.Subscribe(MQ_MSG_TRACK_SNAP, onTrackSnap)
	_mq.Subscribe(MQ_MSG_TRACK_CFG, onTrackCfg)
	_mq.Subscribe(MQ_MSG_TRACK_START, onTrackStart)
	_mq.Subscribe(MQ_MSG_TRACK_STOP, onTrackStop)
	_mq.Subscribe(MQ_MSG_TRACK_FOCUS, onTrackFocus)
	_mq.Subscribe(MQ_MSG_TRACK_PAUSE, onTrackParse)
	_mq.Subscribe(MQ_MSG_TRACK_RESUME, onTrackResume)
	_mq.Subscribe(MQ_MSG_TRACK_RESET, onTrackReset)
	_mq.Subscribe(MQ_MSG_TRACK_SYNC, onTrackSync)

	_startTime = time.Now().UnixMilli()
	_localIp = getLocalIp()
	_localPort = port
	_imgPath = data.GetShadow("fht")
	if fs.HasDir(_imgPath) {
		fs.AddDir(_imgPath)
	}

	go reportHeartbeat()
}

func reportHeartbeat() {
	msgHeartbeat := MsgHeartbeat{}
	msgHeartbeat.SIP = _localIp
	msgHeartbeat.StartTime = _startTime
	for {
		msgHeartbeat.SysTime = time.Now().UnixMilli()
		b, _ := json.Marshal(&msgHeartbeat)
		if e := _mq.Publish(MQ_MSG_HEARTBEAT, b); e != nil {
			logrus.Errorf("mq publish heartbeat error: %s", e.Error())
		}
		time.Sleep(time.Second)
	}
}

func isMyMsg(rip string) bool {
	return rip == _localIp
}

func ProcFhtVideo(filepath, cid, logId string) error {
	msgAdd := MsgTrackAdd{
		CID:           cid,
		LocalFilePath: filepath,
	}
	proc := doTrackAdd(&msgAdd, "")

	tz := TrackZone{
		ZID:       cid + "_0",
		Direction: 0,
		XYXY:      []float32{0.0, 0.0, 1.0, 1.0},
	}
	msgCfg := MsgTrackCfg{
		CID:   cid,
		Zones: []TrackZone{tz},
	}

	doTrackCfg(&msgCfg, proc, "")

	doFocus(cid)

	doTrackStart(proc, "")

	return nil
}

func procTrackResp(reply, code, msg, data string) {
	if reply == "" {
		return
	}

	msgResp := MsgTrackResp{
		Code: code,
		Msg:  msg,
		Data: data,
	}

	b, _ := json.Marshal(&msgResp)
	if e := _mq.Publish(reply, b); e != nil {
		logrus.Errorf("mq publish track resp error: %s", e.Error())
	}
}

func procTrackStat(cid, code, msg, data string) {
	msgStat := MsgTrackStat{
		CID:  cid,
		Code: code,
		Msg:  msg,
		Data: data,
	}

	b, _ := json.Marshal(&msgStat)
	if e := _mq.Publish(MQ_MSG_TRACK_STAT, b); e != nil {
		logrus.Errorf("mq publish track stat error: %s", e.Error())
	}
}

func getTrackZone(proc *TrackerProc, p image.Point) string {
	for _, zone := range proc.zones {
		d := gocv.ContourDist(zone.pts, p)
		if d > 0 {
			return zone.ZID
		}
	}
	return ""
}

func needDrawImage(proc *TrackerProc) bool {
	if proc.focus {
		return true
	}
	if time.Now().UnixMilli()-proc.imgTime > 1000 {
		return true
	}
	return false
}

func procTrackData(proc *TrackerProc, seq int64, img gocv.Mat, tt []*STrack) error {
	td := MsgTrackData{}
	td.CID = proc.cid
	td.T = time.Now().UnixNano() / 1e6
	td.L = 0
	td.Objs = []ObjData{}

	for _, t := range tt {
		center := image.Pt(int(t.Tlwh()[0]+t.Tlwh()[2]/2), int(t.Tlwh()[1]+t.Tlwh()[3]/2))
		zid := getTrackZone(proc, center)
		if zid == "" {
			continue
		}

		od := ObjData{}
		od.Obj = t.Cls
		od.TID = fmt.Sprintf("%d", t.TrackID)
		od.X = center.X
		od.Y = center.Y
		od.ZID = zid
		td.Objs = append(td.Objs, od)

		drawColor := colorTable[t.TrackID%len(colorTable)]
		x, y, w, h := int(t.Tlwh()[0]), int(t.Tlwh()[1]), int(t.Tlwh()[2]), int(t.Tlwh()[3])
		gocv.Rectangle(&img, image.Rect(x, y, x+w, y+h), drawColor, 2)
		gocv.Circle(&img, image.Pt(od.X, od.Y), 4, drawColor, 4)
		gocv.PutText(&img, od.TID, image.Pt(od.X, od.Y), gocv.FontHersheyPlain, 2.0, drawColor, 2.0)
	}
	if needDrawImage(proc) {
		pp := [][]image.Point{}
		for _, zone := range proc.zones {
			pp = append(pp, zone.pts)
		}
		gocv.Polylines(&img, pp, true, color.RGBA{255, 255, 255, 100}, 1)

		fp := fmt.Sprintf("%s_%d.jpg", proc.cid, td.T)
		gocv.IMWrite(path.Join(_imgPath, fp), img)
		imgUrl := fmt.Sprintf("http://%s:%d/fht/output/%s", _localIp, _localPort, fp)
		proc.imgUrl = imgUrl
		proc.imgSize = image.Pt(img.Cols(), img.Rows())
		proc.imgTime = time.Now().UnixMilli()
		td.ImgUrl = imgUrl
	}
	img.Release()

	proc.sendQ.Store(seq, &td)

	return nil
}

func pushTrackData(proc *TrackerProc) {
	var lastT, curT, lastSeq int64
	lastT = int64(time.Now().UnixNano() / 1e6)
	lastSeq = 0

	for {
		if proc.terminate {
			break
		}
		v, ok := proc.sendQ.Load(proc.sendSeq)
		if ok {
			td := v.(*MsgTrackData)
			bytes, _ := json.Marshal(td)
			err := _mq.Publish(MQ_MSG_TRACK_DATA, bytes)
			if err != nil {
				logrus.Infof("fail to push track: %s", bytes)
			} else {
				// logrus.Infof("push track %d: %s\n", proc.sendSeq, bytes)
				proc.sendQ.Delete(proc.sendSeq)
				proc.sendSeq++
			}
		}

		if !ok {
			curT = int64(time.Now().UnixNano() / 1e6)
			if curT-lastT > 1000 {
				if proc.sendSeq != lastSeq && proc.sendSeq > 2 {
					logrus.Infof("fps[%s]: %d", proc.cid, proc.sendSeq-lastSeq)
				}

				lastSeq = proc.sendSeq
				lastT = curT
			}
			time.Sleep(time.Millisecond * 10)
		}
	}
}

func doTrackFileAdd(proc *TrackerProc, reply, filePath string) error {
	var err error
	code := CODE_SUCCESS
	defer func() {
		if err != nil {
			procTrackResp(reply, code, err.Error(), "")
		} else {
			procTrackResp(reply, code, "", "")
		}
	}()

	logrus.Infof("track file: %s", filePath)
	proc.vio, err = gocv.VideoCaptureFile(filePath)
	if err != nil {
		code = CODE_TRACK_VIDEO_OPEN_FAILED
		return err
	}

	if !proc.vio.IsOpened() {
		err = fmt.Errorf("open video failed")
		code = CODE_TRACK_VIDEO_OPEN_FAILED
		return err
	}

	img := gocv.NewMat()
	if !proc.vio.Read(&img) {
		err = fmt.Errorf("read video failed")
		code = CODE_TRACK_VIDEO_READ_FAILED
		return err
	}

	fp := fmt.Sprintf("%s_%d.jpg", proc.cid, time.Now().UnixMilli())
	gocv.IMWrite(path.Join(_imgPath, fp), img)
	imgUrl := fmt.Sprintf("http://%s:%d/fht/output/%s", _localIp, _localPort, fp)
	proc.imgUrl = imgUrl
	proc.imgSize = image.Pt(img.Cols(), img.Rows())
	proc.imgTime = time.Now().UnixMilli()
	img.Release()

	return nil
}

func doTrackSnap(proc *TrackerProc, reply string) {
	if proc.vio == nil || !proc.vio.IsOpened() {
		procTrackResp(reply, CODE_TRACK_VIDEO_OPEN_FAILED, "video not opened", "")
		return
	}

	img := gocv.NewMat()
	if !proc.vio.Read(&img) {
		procTrackResp(reply, CODE_TRACK_VIDEO_READ_FAILED, "failed to read video frame", "")
		return
	}

	fp := fmt.Sprintf("%s_snap_%d.jpg", proc.cid, time.Now().UnixMilli())
	gocv.IMWrite(path.Join(_imgPath, fp), img)
	imgUrl := fmt.Sprintf("http://%s:%d/fht/output/%s", _localIp, _localPort, fp)
	img.Release()

	procTrackResp(reply, CODE_SUCCESS, "", imgUrl)
}

func doTrackAdd(p *MsgTrackAdd, reply string) *TrackerProc {
	proc := new(TrackerProc)
	proc.tracker = new(BYTETracker)
	proc.tracker.DetLowConfThresh = 0.1
	proc.tracker.DetHighConfThresh = 0.7
	proc.zones = []TrackZone{}
	proc.cid = p.CID
	proc.startTime = time.Now().UnixMilli()
	proc.logId = fmt.Sprintf("%s_%lld", p.CID, proc.startTime)
	proc.curSeq = 0
	proc.sendSeq = 1

	_procMap.Store(p.CID, proc)

	if p.LocalFilePath != "" {
		go doTrackFileAdd(proc, reply, p.LocalFilePath)
	} else if p.streamUrl != "" {
		// /todo
		procTrackResp(reply, CODE_SUCCESS, "", "")
	} else {
		procTrackResp(reply, CODE_PARAM_ERROR, "track type not supported", "")
	}

	go pushTrackData(proc)

	return proc
}

func doTrackDel(proc *TrackerProc, reply string) {
	logrus.Infof("track delete: %s", proc.cid)
	defer procTrackResp(reply, CODE_SUCCESS, "", "")
	if proc.terminate {
		return
	}

	proc.terminate = true
	if proc.vio != nil && proc.vio.IsOpened() {
		proc.vio.Close()
	}

	_procMap.Delete(proc.cid)
}

func doTrackCfg(p *MsgTrackCfg, proc *TrackerProc, reply string) {
	if len(p.Zones) == 0 {
		procTrackResp(reply, CODE_PARAM_ERROR, "track zones not exist", "")
		return
	}

	minX, minY, maxX, maxY := -1, -1, -1, -1
	proc.zones = p.Zones
	for i, zone := range proc.zones {
		var pts []image.Point
		for j := 0; j < len(zone.XYXY); j += 2 {
			x := int(zone.XYXY[j] * float32(proc.imgSize.X))
			y := int(zone.XYXY[j+1] * float32(proc.imgSize.Y))
			pts = append(pts, image.Pt(x, y))
			if minX < 0 || minX > x {
				minX = x
			}
			if minY < 0 || minY > y {
				minY = y
			}
			if x > maxX {
				maxX = x
			}
			if y > maxY {
				maxY = y
			}
		}
		proc.zones[i].pts = pts
	}
	proc.roi = image.Rect(minX, minY, maxX, maxY)

	procTrackResp(reply, CODE_SUCCESS, "", "")
}

func doTrackStart(proc *TrackerProc, reply string) {
	if len(proc.zones) == 0 {
		procTrackResp(reply, CODE_TRACK_ZONE_NOT_EXIST, "track zoon not exist", "")
		return
	}

	proc.started = true
	proc.pause = false

	go func() {
		// lastT := int64(time.Now().UnixNano() / 1e6)
		for {
			if proc.terminate {
				break
			}
			img := gocv.NewMat()
			if !proc.vio.Read(&img) {
				break
			}
			// now := int64(time.Now().UnixNano() / 1e6)
			// logrus.Infof("read time: %d\n", now-lastT)
			// lastT = now
			imgR := img.Region(proc.roi)
			dets, err := doDetV2("FHTD", &imgR, proc.roi.Min, proc.logId)
			if err != nil {
				// logrus.Errorf("FHTD error: %s", err.Error())
				// continue
			}
			imgR.Release()
			// now = int64(time.Now().UnixNano() / 1e6)
			// logrus.Infof("detect time: %d\n", now-lastT)
			// lastT = now
			tt := proc.tracker.Update(dets)
			// now = int64(time.Now().UnixNano() / 1e6)
			// logrus.Infof("track time: %d\n", now-lastT)
			// lastT = now

			proc.curSeq++
			go procTrackData(proc, proc.curSeq, img, tt)

			for {
				if !proc.pause {
					break
				}
				time.Sleep(time.Millisecond * 10)
			}
		}

		doTrackDel(proc, "")

		procTrackStat(proc.cid, CODE_TRACK_VIDEO_FINISH, "video end", "")
	}()

	procTrackResp(reply, CODE_SUCCESS, "", "")
}

func doTrackStop(proc *TrackerProc, reply string) {
	proc.tracker = new(BYTETracker)
	proc.curSeq = 0
	proc.sendSeq = 1
	proc.sendQ.Clear()
	proc.pause = true

	procTrackResp(reply, CODE_SUCCESS, "", "")
}

func onTrackAdd(msg *nats.Msg) {
	p := MsgTrackAdd{}
	err := json.Unmarshal(msg.Data, &p)
	if err != nil {
		logrus.Errorf("json.Unmarshal error: %s", err.Error())
		return
	}

	if !isMyMsg(p.RIP) {
		return
	}

	_, ok := _procMap.Load(p.CID)
	if ok {
		procTrackResp(msg.Reply, CODE_TRACK_ALREADY_EXIST, "track already exist", "")
		return
	}

	go doTrackAdd(&p, msg.Reply)
}

func onTrackDel(msg *nats.Msg) {
	p := MsgTrackDel{}
	err := json.Unmarshal(msg.Data, &p)
	if err != nil {
		logrus.Errorf("json.Unmarshal error: %s", err.Error())
		return
	}

	if !isMyMsg(p.RIP) {
		return
	}

	v, ok := _procMap.Load(p.CID)
	if !ok {
		procTrackResp(msg.Reply, CODE_TRACK_NOT_EXIST, "track not exist", "")
		return
	}
	proc := v.(*TrackerProc)
	go doTrackDel(proc, msg.Reply)
}

func onTrackSnap(msg *nats.Msg) {
	p := MsgTrackSnap{}
	err := json.Unmarshal(msg.Data, &p)
	if err != nil {
		logrus.Errorf("json.Unmarshal error: %s", err.Error())
		return
	}

	if !isMyMsg(p.RIP) {
		return
	}

	v, ok := _procMap.Load(p.CID)
	if !ok {
		procTrackResp(msg.Reply, CODE_TRACK_NOT_EXIST, "track not exist", "")
		return
	}
	proc := v.(*TrackerProc)
	go doTrackSnap(proc, msg.Reply)
}

func onTrackCfg(msg *nats.Msg) {
	p := MsgTrackCfg{}
	err := json.Unmarshal(msg.Data, &p)
	if err != nil {
		logrus.Errorf("json.Unmarshal error: %s", err.Error())
		return
	}

	if !isMyMsg(p.RIP) {
		return
	}

	v, ok := _procMap.Load(p.CID)
	if !ok {
		procTrackResp(msg.Reply, CODE_TRACK_NOT_EXIST, "track not exist", "")
		return
	}
	proc := v.(*TrackerProc)

	go doTrackCfg(&p, proc, msg.Reply)
}

func onTrackStart(msg *nats.Msg) {
	p := MsgTrackStart{}
	err := json.Unmarshal(msg.Data, &p)
	if err != nil {
		logrus.Errorf("json.Unmarshal error: %s", err.Error())
		procTrackResp(msg.Reply, CODE_PARAM_ERROR, err.Error(), "")
		return
	}

	if !isMyMsg(p.RIP) {
		return
	}

	v, ok := _procMap.Load(p.CID)
	if !ok {
		procTrackResp(msg.Reply, CODE_TRACK_NOT_EXIST, "track not exist", "")
		return
	}
	proc := v.(*TrackerProc)
	if proc.started {
		procTrackResp(msg.Reply, CODE_TRACK_ALREADY_STARTED, "track already started", "")
		return
	}

	go doTrackStart(proc, msg.Reply)
}

func onTrackStop(msg *nats.Msg) {
	p := MsgTrackStop{}
	err := json.Unmarshal(msg.Data, &p)
	if err != nil {
		logrus.Errorf("json.Unmarshal error: %s", err.Error())
		return
	}

	if !isMyMsg(p.RIP) {
		return
	}

	v, ok := _procMap.Load(p.CID)
	if !ok {
		procTrackResp(msg.Reply, CODE_TRACK_NOT_EXIST, "track not exist", "")
		return
	}
	proc := v.(*TrackerProc)

	go doTrackStop(proc, msg.Reply)
}

func doFocus(cid string) {
	for _, v := range _procMap.Range {
		proc := v.(*TrackerProc)
		if proc.cid == cid {
			proc.focus = true
		} else {
			proc.focus = false
		}
	}
}

func onTrackFocus(msg *nats.Msg) {
	p := MsgTrackFocus{}
	err := json.Unmarshal(msg.Data, &p)
	if err != nil {
		logrus.Errorf("json.Unmarshal error: %s", err.Error())
		return
	}

	if !isMyMsg(p.RIP) {
		return
	}

	v, ok := _procMap.Load(p.CID)
	if !ok {
		procTrackResp(msg.Reply, CODE_TRACK_NOT_EXIST, "track not exist", "")
		return
	}
	proc := v.(*TrackerProc)

	doFocus(proc.cid)
	procTrackResp(msg.Reply, CODE_SUCCESS, "", "")
}

func onTrackParse(msg *nats.Msg) {
	p := MsgTrackPause{}
	err := json.Unmarshal(msg.Data, &p)
	if err != nil {
		logrus.Errorf("json.Unmarshal error: %s", err.Error())
		procTrackResp(msg.Reply, CODE_PARAM_ERROR, err.Error(), "")
		return
	}

	if !isMyMsg(p.RIP) {
		return
	}

	v, ok := _procMap.Load(p.CID)
	if !ok {
		procTrackResp(msg.Reply, CODE_TRACK_NOT_EXIST, "track not exist", "")
		return
	}
	proc := v.(*TrackerProc)

	proc.pause = true
	procTrackResp(msg.Reply, CODE_SUCCESS, "", "")
}

func onTrackResume(msg *nats.Msg) {
	p := MsgTrackResume{}
	err := json.Unmarshal(msg.Data, &p)
	if err != nil {
		logrus.Errorf("json.Unmarshal error: %s", err.Error())
		return
	}

	if !isMyMsg(p.RIP) {
		return
	}

	v, ok := _procMap.Load(p.CID)
	if !ok {
		procTrackResp(msg.Reply, CODE_TRACK_NOT_EXIST, "track not exist", "")
		return
	}
	proc := v.(*TrackerProc)

	proc.pause = false
	procTrackResp(msg.Reply, CODE_SUCCESS, "", "")
}

func onTrackReset(msg *nats.Msg) {
	p := MsgTrackReset{}
	err := json.Unmarshal(msg.Data, &p)
	if err != nil {
		logrus.Errorf("json.Unmarshal error: %s", err.Error())
		return
	}

	if !isMyMsg(p.RIP) {
		return
	}

	for _, v := range _procMap.Range {
		proc := v.(*TrackerProc)
		doTrackDel(proc, "")
	}
	procTrackResp(msg.Reply, CODE_SUCCESS, "", "")
}

func onTrackSync(msg *nats.Msg) {
	p := MsgTrackSync{}
	err := json.Unmarshal(msg.Data, &p)
	if err != nil {
		logrus.Errorf("json.Unmarshal error: %s", err.Error())
		return
	}

	if !isMyMsg(p.RIP) {
		return
	}

	for _, v := range _procMap.Range {
		proc := v.(*TrackerProc)
		procTrackResp(msg.Reply, CODE_SUCCESS, "", proc.imgUrl)
	}
}

func getLocalIp() string {
	interfaces, err := net.Interfaces()
	if err != nil {
		panic(err)
	}

	for _, iface := range interfaces {
		if iface.Flags&net.FlagUp == 0 {
			continue
		}
		if iface.Flags&net.FlagLoopback != 0 {
			continue
		}
		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}
		for _, addr := range addrs {
			var ip net.IP
			switch v := addr.(type) {
			case *net.IPNet:
				ip = v.IP
			case *net.IPAddr:
				ip = v.IP
			}
			if ip.IsLoopback() || ip.To4() == nil {
				continue
			}
			return ip.String()
		}
	}
	return "127.0.0.1"
}
