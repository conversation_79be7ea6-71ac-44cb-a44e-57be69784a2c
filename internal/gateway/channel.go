package gateway

import "fmt"

const (
	channelEdit = "DEVICE_CHANNEL_EDIT"
)

type Channel struct {
	Tdxh  int64  `json:"tdxh"`
	Tdmc  string `json:"tdmc"`
	Sbxh  int64  `json:"sbxh"`
	Tdlx  string `json:"tdlx"`
	Zxzt  int    `json:"zxzt"`
	Sxjbh string `json:"sxjbh"`
}

type ChannelEditReq struct {
	Param
	Tdxh int64  `json:"tdxh"`
	Tdmc string `json:"tdmc"`
}

func (c *client) EditChannel(req *ChannelEditReq) error {
	values, err := c.buildValues(channelEdit, req)
	if err != nil {
		return err
	}
	_, err = c.do(values, nil)
	if err != nil {
		return fmt.Errorf("c.do editChannel failed: %v", err)
	}
	return nil
}
