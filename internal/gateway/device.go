package gateway

import "fmt"

const (
	deviceQuery  = "DEVICE_QUERY"
	deviceDetail = "DEVICE_DETAIL_QUERY"
	channelRtsp  = "DEVICE_CHANNEL_REAL_RTSP"
)

type QueryDeviceReq struct {
	PageReq
	Pxyj string `json:"pxyj"`
	Pxfs string `json:"pxfs"`
}

type DeviceQueryResult struct {
	Sbxh  int64  `json:"sbxh"`
	Ptxh  int64  `json:"ptxh"`
	Sbcs  string `json:"sbcs"`
	Sbip  string `json:"sbip"`
	Sbdkh int    `json:"sbdkh"`
	Sbyhm string `json:"sbyhm"`
	Sbmm  string `json:"sbmm"`
}

func (c *client) QueryDevices(req *QueryDeviceReq) ([]DeviceQueryResult, *PageResp, error) {
	type DevicePageResp struct {
		PageResp
		Results []DeviceQueryResult `json:"results"`
	}
	values, err := c.buildValues(deviceQuery, req)
	if err != nil {
		return nil, nil, err
	}
	resp, err := c.do(values, &DevicePageResp{})
	if err != nil {
		return nil, nil, fmt.Errorf("c.do queryDevices failed: %w", err)
	}
	respData, ok := resp.(*DevicePageResp)
	if !ok {
		return nil, nil, fmt.Errorf("%v call failed", deviceQuery)
	}
	if respData.TotalCount <= 0 {
		return nil, &respData.PageResp, err
	}
	return respData.Results, &respData.PageResp, nil
}

type DeviceDetailResult struct {
	Sbxh     int64     `json:"sbxh"`
	Ptxh     int64     `json:"ptxh"`
	Zzbh     string    `json:"zzbh"`
	Sbcs     string    `json:"sbcs"`
	Sbip     string    `json:"sbip"`
	Sbdkh    int       `json:"sbdkh"`
	Sbyhm    string    `json:"sbyhm"`
	Sbmm     string    `json:"sbmm"`
	Channels []Channel `json:"sbtd"`
}

func (c *client) QueryDeviceDetail(sbxh int64) (*DeviceDetailResult, error) {
	type DeviceDetailReq struct {
		Param
		Sbxh int64 `json:"sbxh"`
	}
	values, err := c.buildValues(deviceDetail, &DeviceDetailReq{
		Sbxh: sbxh,
	})
	if err != nil {
		return nil, err
	}
	resp, err := c.do(values, &DeviceDetailResult{})
	if err != nil {
		return nil, fmt.Errorf("c.do queryDeviceDetail failed: %v", err)
	}
	return resp.(*DeviceDetailResult), nil
}

func (c *client) QueryChannelRealRtsp(sxjbh string) (string, error) {
	type ChannelRtspReq struct {
		Param
		Sxjbh string `json:"sxjbh"`
		Mllx  int    `json:"mllx"`
	}
	values, err := c.buildValues(channelRtsp, &ChannelRtspReq{
		Sxjbh: sxjbh,
		Mllx:  0,
	})
	if err != nil {
		return "", err
	}
	resp, err := c.do(values, nil)
	if err != nil {
		return "", fmt.Errorf("c.do queryChannelRealRtsp failed: %v", err)
	}
	return resp.(string), nil
}
