package gateway

import "fmt"

const (
	sync         = "DEVICE_PLAT_SYNC"
	syncProgress = "DEVICE_PLAT_SYNC_PROGRESS"
)

func (c *client) Sync() (string, error) {
	type SyncResp struct {
		Tbbh string `json:"tbbh"`
	}
	values, err := c.buildValues(sync, &Param{})
	if err != nil {
		return "", err
	}
	resp, err := c.do(values, &SyncResp{})
	if err != nil {
		return "", fmt.Errorf("c.do sync failed: %v", err)
	}
	return resp.(*SyncResp).Tbbh, err
}

type Xxsz struct {
	Ptmc string `json:"ptmc"`
	Tbcg int    `json:"tbcg"`
}
type SyncProgressResp struct {
	Tbjd   int    `json:"tbjd"`
	Tbxxsz []Xxsz `json:"tbxxsz"`
}

func (c *client) SyncProgress(tbbh string) (*SyncProgressResp, error) {
	type SyncProgressReq struct {
		Param
		Tbbh string `json:"tbbh"`
	}
	values, err := c.buildValues(syncProgress, &SyncProgressReq{
		Tbbh: tbbh,
	})
	if err != nil {
		return nil, err
	}
	resp, err := c.do(values, &SyncProgressResp{})
	if err != nil {
		return nil, fmt.Errorf("c.do syncProgress failed: %v", err)
	}
	return resp.(*SyncProgressResp), err
}
