package gateway

import "fmt"

const login = "LOGIN"

type Login struct {
	Xh int `json:"xh"`
}
type LoginResp struct {
	Token string `json:"token"`
}

func (c *client) Login() (string, error) {
	values, err := c.buildValues(login, &Login{Xh: 1})
	if err != nil {
		return "", err
	}
	resp, err := c.do(values, &LoginResp{})
	if err != nil {
		return "", fmt.<PERSON>rro<PERSON>("c.do login failed: %v", err)
	}
	return resp.(*LoginResp).Token, nil
}
