package gateway

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"fmt"
)

// SignWithRSA 使用 RSA 私钥对字符串进行 SHA256withRSA 签名
func SignWithRSA(ori, privateKey string) (string, error) {
	rsaPriv, err := getPrivateKey(privateKey)
	if err != nil {
		return "", err
	}

	hash := sha256.Sum256([]byte(ori))
	signature, err := rsa.SignPKCS1v15(nil, rsaPriv, crypto.SHA256, hash[:])
	if err != nil {
		return "", fmt.Errorf("签名失败: %v", err)
	}
	return base64.StdEncoding.EncodeToString(signature), nil
}

func getPrivateKey(key string) (*rsa.PrivateKey, error) {
	keyBytes, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		return nil, fmt.Errorf("解码 Base64 私钥失败: %v", err)
	}

	priv, err := x509.ParsePKCS8PrivateKey(keyBytes)
	if err != nil {
		return nil, fmt.Errorf("解析 PKCS#8 私钥失败: %v", err)
	}

	rsaPriv, ok := priv.(*rsa.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("私钥不是 RSA 类型")
	}

	return rsaPriv, nil
}
