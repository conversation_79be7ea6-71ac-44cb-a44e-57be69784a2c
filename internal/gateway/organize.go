package gateway

import (
	"fmt"
)

const (
	organizeQuery = "DEVICE_PLAT_ORGANIZE_QUERY"
)

type OrganizeQueryReq struct {
	Param
	Ptxh  int64  `json:"ptxh"`
	Fzzbh string `json:"fzzbh"`
}

type OrganizeQueryResult struct {
	Ptxh     int64                  `json:"ptxh"`
	Zzbh     string                 `json:"zzbh"`
	Zzmc     string                 `json:"zzmc"`
	Yzzz     int                    `json:"yzzz"`
	Fzzbh    string                 `json:"fzzbh"`
	Zzbsm    string                 `json:"zzbsm"`
	Children []*OrganizeQueryResult `json:"children"`
}

func (c *client) QueryAllOrganizes(ptxh int64) ([]*OrganizeQueryResult, error) {
	// 1. 查询顶层组织
	topLevelOrgs, err := c.queryOrganizes(&OrganizeQueryReq{
		Ptxh: ptxh,
	})
	if err != nil {
		return nil, err
	}

	// 2. 为每个顶层组织递归地填充其子组织
	for _, org := range topLevelOrgs {
		if err := c.populateChildren(ptxh, org); err != nil {
			return nil, err
		}
	}

	return topLevelOrgs, nil
}

// populateChildren 递归地查询并填充一个组织的子组织
func (c *client) populateChildren(ptxh int64, parent *OrganizeQueryResult) error {
	if parent.Yzzz == 0 {
		return nil
	}
	// 查询直接子组织
	children, err := c.queryOrganizes(&OrganizeQueryReq{
		Ptxh:  ptxh,
		Fzzbh: parent.Zzbh,
	})
	if err != nil {
		return fmt.Errorf("查询 ptxh %d, fzzbh %s 的子组织失败: %w", ptxh, parent.Zzbh, err)
	}

	// 附加子组织并为每个子组织继续递归
	parent.Children = children
	for _, child := range children {
		if err := c.populateChildren(ptxh, child); err != nil {
			return err
		}
	}
	return nil
}

func (c *client) queryOrganizes(req *OrganizeQueryReq) ([]*OrganizeQueryResult, error) {
	values, err := c.buildValues(organizeQuery, req)
	if err != nil {
		return nil, err
	}
	resp, err := c.do(values, &[]*OrganizeQueryResult{})
	if err != nil {
		return nil, fmt.Errorf("c.do queryOrganizes failed: %v", err)
	}

	if resp == nil {
		return nil, nil
	}

	respData, ok := resp.(*[]*OrganizeQueryResult)
	if !ok {
		return nil, fmt.Errorf("resp.([]*OrganizeQueryResult): invalid response")
	}
	return *respData, nil
}
