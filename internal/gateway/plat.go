package gateway

import (
	"fmt"
)

const (
	platQuery  = "DEVICE_PLAT_QUERY"
	platAdd    = "DEVICE_PLAT_ADD"
	platUpdate = "DEVICE_PLAT_EDIT"
	platDelete = "DEVICE_PLAT_DELETE"
)

type PlatAddReq struct {
	Param
	Ptbh  string `json:"ptbh"`
	Ptmc  string `json:"ptmc"`
	Ptcs  string `json:"ptcs"`
	Ptip  string `json:"ptip"`
	Ptdkh int    `json:"ptdkh"`
	Ptyhm string `json:"ptyhm"`
	Ptmm  string `json:"ptmm"`
}

func (c *client) AddPlat(p *PlatAddReq) (int64, error) {
	values, err := c.buildValues(platAdd, p)
	if err != nil {
		return 0, err
	}
	_, err = c.do(values, nil)
	if err != nil {
		return 0, fmt.<PERSON><PERSON><PERSON>("c.do addPlat failed: %v", err)
	}
	ptxh, err := c.QueryPtxhPlatByPtbh(p.Ptbh)
	return ptxh, err
}

type PlatUpdateReq struct {
	Param
	Ptxh  int64  `json:"ptxh"`
	Ptbh  string `json:"ptbh"`
	Ptmc  string `json:"ptmc"`
	Ptcs  string `json:"ptcs"`
	Ptip  string `json:"ptip"`
	Ptdkh int    `json:"ptdkh"`
	Ptyhm string `json:"ptyhm"`
	Ptmm  string `json:"ptmm"`
}

func (c *client) EditPlat(p *PlatUpdateReq) error {
	values, err := c.buildValues(platUpdate, p)
	if err != nil {
		return err
	}
	_, err = c.do(values, nil)
	if err != nil {
		return fmt.Errorf("c.do editPlat failed: %v", err)
	}
	return nil
}

type PlatDeleteReq struct {
	Param
	Ptbh string `json:"ptbh"`
}

func (c *client) DeletePlat(ptbh string) error {
	values, err := c.buildValues(platDelete, &PlatDeleteReq{
		Ptbh: ptbh,
	})
	if err != nil {
		return err
	}
	_, err = c.do(values, nil)
	if err != nil {
		return fmt.Errorf("c.do deletePlat failed: %v", err)
	}
	return nil
}

type PlatQueryResult struct {
	Ptxh  int64  `json:"ptxh"`
	Ptbh  string `json:"ptbh"`
	Ptmc  string `json:"ptmc"`
	Ptcs  string `json:"ptcs"`
	Ptip  string `json:"ptip"`
	Ptdkh int    `json:"ptdkh"`
	Ptyhm string `json:"ptyhm"`
	Ptmm  string `json:"ptmm"`
}

func (c *client) QueryPtxhPlatByPtbh(ptbh string) (int64, error) {
	type PlatQueryReq struct {
		PageReq
		Ptbh string `json:"ptbh"`
	}
	plats, err := c.QueryPlats(&PlatQueryReq{
		PageReq: PageReq{Page: 1, PageSize: 1},
		Ptbh:    ptbh,
	})
	if err != nil || plats == nil {
		return 0, fmt.Errorf("c.do queryPtxhPlatByPtbh failed: %v", err)
	}
	return plats[0].Ptxh, nil
}

func (c *client) QueryPlatDetail(ptxh int64) (*PlatQueryResult, error) {
	type PlatDetailReq struct {
		PageReq
		Ptxh int64 `json:"ptxh"`
	}
	plats, err := c.QueryPlats(&PlatDetailReq{
		PageReq: PageReq{Page: 1, PageSize: 1},
		Ptxh:    ptxh,
	})
	if err != nil || plats == nil {
		return nil, fmt.Errorf("c.do queryPlatDetail failed: %v", err)
	}
	return &plats[0], nil
}

func (c *client) QueryPlats(req interface{}) ([]PlatQueryResult, error) {
	type PlatPageResp struct {
		PageResp
		Results []PlatQueryResult `json:"results"`
	}
	values, err := c.buildValues(platQuery, req)
	if err != nil {
		return nil, err
	}
	resp, err := c.do(values, &PlatPageResp{})
	if err != nil {
		return nil, fmt.Errorf("c.do queryPlats failed: %v", err)
	}
	respData, ok := resp.(*PlatPageResp)
	if !ok {
		return nil, fmt.Errorf("resp.(*PlatPageResp): invalid response")
	}
	if respData.TotalCount <= 0 {
		return nil, nil
	}
	return respData.Results, nil
}
