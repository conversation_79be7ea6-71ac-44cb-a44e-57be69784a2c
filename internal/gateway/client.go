package gateway

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"reflect"
	"time"
)

const (
	successCode = "1"
)

type client struct {
}

type Head struct {
	Czlx  string `json:"czlx"`
	Qqsjc string `json:"qqsjc"`
}

type Param struct {
	Token string `json:"token"`
}

type Resp struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type PageReq struct {
	Param
	Page     int `json:"page"`
	PageSize int `json:"pageSize"`
}

type PageResp struct {
	PageReq
	PageCount  int `json:"pageCount"`
	TotalCount int `json:"totalCount"`
}

func (c *client) do(values *url.Values, t interface{}) (interface{}, error) {
	resp, err := http.PostForm(config.GetUrl(), *values)
	if err != nil {
		err = fmt.Errorf("http.PostForm failed: %v", err)
		return nil, err
	}

	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		err = fmt.Errorf("io.ReadAll failed: %v", err)
		return nil, err
	}

	if len(body) == 0 {
		return nil, fmt.Errorf("body is empty")
	}

	respData := Resp{
		Data: t,
	}
	err = json.Unmarshal(body, &respData)
	if err != nil {
		err = fmt.Errorf("json.Unmarshal failed: %v, %v", body, err)
		return nil, err
	}
	if respData.Code != successCode {
		err = fmt.Errorf("respData.Code: %s, respData.Message: %s", respData.Code, respData.Message)
		return nil, err
	}
	return respData.Data, nil
}

func (c *client) buildValues(czlx string, param interface{}) (*url.Values, error) {
	head := Head{
		Czlx:  czlx,
		Qqsjc: getCurrentDateTime(),
	}
	headJSON, err := json.Marshal(head)
	if err != nil {
		panic(fmt.Errorf("json.Marshal(head) failed: %v", err))
	}

	var paramJSON []byte
	if !isNilParam(param) {
		elem := reflect.ValueOf(param).Elem()
		tokenField := elem.FieldByName("Token")
		if tokenField.IsValid() && tokenField.CanSet() {
			t, err := c.Login()
			if err != nil {
				return nil, err
			}
			tokenField.SetString(t)
		}
		pj, err := json.Marshal(param)
		if err != nil {
			return nil, fmt.Errorf("json.Marshal(param) failed: %v", err)
		}
		paramJSON = pj
	}

	sign := ""
	if config.RsaPrivateKey != "" {
		sign, err = SignWithRSA(string(paramJSON)+"#"+head.Qqsjc, config.RsaPrivateKey)
		if err != nil {
			panic(fmt.Errorf("SignWithRSA failed: %v", err))
		}
	}

	values := url.Values{}
	values.Set("head", string(headJSON))
	values.Set("param", string(paramJSON))
	values.Set("sign", sign)
	return &values, nil
}

func isNilParam(param interface{}) bool {
	if param == nil {
		return true
	}
	rv := reflect.ValueOf(param)
	if !rv.IsValid() {
		return true
	}
	if rv.Kind() == reflect.Ptr || rv.Kind() == reflect.Slice || rv.Kind() == reflect.Map ||
		rv.Kind() == reflect.Chan || rv.Kind() == reflect.Interface || rv.Kind() == reflect.Func {
		return rv.IsNil()
	}
	if rv.IsZero() {
		return true
	}
	if rv.Kind() == reflect.Ptr {
		param = rv.Elem()
	}
	return false
}

func getCurrentDateTime() string {
	return time.Now().Format("20060102150405")
}
