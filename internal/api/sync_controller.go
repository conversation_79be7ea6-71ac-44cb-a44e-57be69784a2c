package api

import (
	"fmt"
	"net/http"
	"reflect"
	"stvpp/global"
	"stvpp/internal/gateway"
	"stvpp/internal/modules"
	"time"

	"github.com/iWay7/go-common/cache"
	"github.com/iWay7/go-common/iam"
	"github.com/iWay7/go-common/logger"
	"github.com/iWay7/go-common/misc"
	"github.com/iWay7/go-common/router"
)

type SyncController struct {
}

const (
	SyncBaseURI     = "/sync"
	syncStartKey    = "stvpp:video:sync:start:%v"
	syncProgressKey = "stvpp:video:sync:progress:%v"
	syncEndKey      = "stvpp:video:sync:end:%v"
	syncErrKey      = "stvpp:video:sync:err:%v"
	syncCountKey    = "stvpp:video:sync:count:%v"
	syncSuccess     = "success"
	syncFailed      = "failed"
)

var syncLogger = *logger.NewFileLogger(global.SyncLogFile, 128*1024*1024)

func (controller *SyncController) Routes() []router.Route {
	return []router.Route{
		{
			Method:             http.MethodPost,
			URI:                SyncBaseURI + "/trigger",
			Func:               controller.SyncTrigger,
			OperationType:      router.OperationTypeView,
			OperationName:      "数据同步",
			RequireBody:        router.BodyTypeAny,
			ResponseResultType: reflect.TypeOf(SyncTriggerResp{}),
		},
		{
			Method:             http.MethodPost,
			URI:                SyncBaseURI + "/progress",
			Func:               controller.SyncProgress,
			OperationType:      router.OperationTypeView,
			OperationName:      "查询同步进度",
			RequireBody:        router.BodyTypeJson,
			RequestType:        reflect.TypeOf(SyncProgressReq{}),
			ResponseResultType: reflect.TypeOf(SyncProgressResp{}),
		},
	}
}

type SyncTriggerResp struct {
	Code string `json:"code" valid:"not_empty"`
}

func (controller *SyncController) SyncTrigger(ctx *router.RequestContext) router.Response {
	code, err := gateway.Client.Sync()
	if err != nil {
		return router.NewFailResponse(ErrCallGatewayService, err.Error())
	}
	go sync(code)
	return router.NewSuccessResponse(SyncTriggerResp{Code: code})
}

type SyncProgressReq struct {
	Code string `json:"code" valid:"not_empty"`
}

type SyncMessage struct {
	PlatformName string `json:"platform_name"`
	SyncStatus   int    `json:"sync_status"`
}

type SyncProgressResp struct {
	Progress     int           `json:"progress"`
	Messages     []SyncMessage `json:"messages"`
	StartTime    int64         `json:"start_time"`
	EndTime      int64         `json:"end_time"`
	SuccessCount int64         `json:"success_count"`
	FailCount    int64         `json:"fail_count"`
	ErrMessage   string        `json:"err_message"`
}

func (controller *SyncController) SyncProgress(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*SyncProgressReq)
	resp, err := gateway.Client.SyncProgress(req.Code)
	if err != nil {
		return router.NewFailResponse(ErrCallGatewayService, err.Error())
	}
	messages := make([]SyncMessage, 0, len(resp.Tbxxsz))
	for _, xxsz := range resp.Tbxxsz {
		messages = append(messages, SyncMessage{
			PlatformName: xxsz.Ptmc,
			SyncStatus:   xxsz.Tbcg,
		})
	}
	st, et := int64(0), int64(0)
	ce := cache.GetAndScan(fmt.Sprintf(syncStartKey, req.Code), &st)
	if ce != nil {
		return router.NewFailResponse(ErrSyncIsNotStarted, ce.Error())
	}
	ce = cache.GetAndScan(fmt.Sprintf(syncEndKey, req.Code), &et)
	countKey := fmt.Sprintf(syncCountKey, req.Code)
	sc, fc, pg, em := int64(0), int64(0), 0, ""
	_ = cache.HGet(countKey, syncSuccess, &sc)
	_ = cache.HGet(countKey, syncFailed, &fc)
	_ = cache.GetAndScan(fmt.Sprintf(syncErrKey, req.Code), &em)
	if et != 0 {
		pg = 100
	} else {
		_ = cache.GetAndScan(fmt.Sprintf(syncProgressKey, req.Code), &pg)
	}
	result := SyncProgressResp{
		Progress:     pg,
		Messages:     messages,
		StartTime:    st,
		EndTime:      et,
		SuccessCount: sc,
		FailCount:    fc,
		ErrMessage:   em,
	}
	return router.NewSuccessResponse(result)
}

func sync(code string) {
	var err error
	err = cache.Set(fmt.Sprintf(syncStartKey, code), misc.NowInMillis(), time.Hour*24)
	if err != nil {
		syncLogger.Appendf("[%v] set sync start key failed: %v", code, err.Error())
		_ = cache.Set(fmt.Sprintf(syncErrKey, code), err.Error(), time.Hour*24)
		return
	}
	countKey := fmt.Sprintf(syncCountKey, code)
	page, size := 1, 100
	var devices []gateway.DeviceQueryResult
	var pageResp *gateway.PageResp
	first := true
	for {
		devices, pageResp, err = gateway.Client.QueryDevices(&gateway.QueryDeviceReq{
			PageReq: gateway.PageReq{Page: page, PageSize: size},
			Pxyj:    "sbxh",
			Pxfs:    "asc",
		})
		if err != nil {
			if first {
				syncLogger.Appendf("[%v] query devices failed: %v", code, err.Error())
				_ = cache.Set(fmt.Sprintf(syncErrKey, code), err.Error(), time.Hour*24)
				break
			}
			syncLogger.Appendf("[%v] page [%v] query devices failed: %v", code, page, err.Error())
			break
		}
		syncLogger.Appendf("[%v] query total page: %v", code, pageResp.PageCount)
		first = false
		syncLogger.Appendf("[%v] page [%v] is processing... devices: %+v", code, page, devices)
		_ = cache.Set(fmt.Sprintf(syncProgressKey, code), page/pageResp.PageCount, time.Hour*24)
		var detail *gateway.DeviceDetailResult
		platMap := make(map[int64]*modules.Platform)
		for _, device := range devices {
			detail, err = gateway.Client.QueryDeviceDetail(device.Sbxh)
			if err != nil {
				syncLogger.Appendf("[%v] query device [%v] detail failed: %v", code, device.Sbxh, err.Error())
				_ = cache.HIncr(countKey, syncFailed, 1)
				continue
			}
			if detail == nil {
				syncLogger.Appendf("[%v] query device [%v] detail is nil", code, device.Sbxh)
				_ = cache.HIncr(countKey, syncFailed, 1)
				continue
			}
			channels := detail.Channels
			if len(channels) == 0 {
				syncLogger.Appendf("[%v] device [%v] channels is empty", code, device.Sbxh)
				continue
			}
			syncLogger.Appendf("[%v] device [%v] is processing... detail: %+v", code, device.Sbxh, detail)
			platform := syncPlat(code, detail.Ptxh, platMap)
			if platform.Id == 0 {
				_ = cache.HIncr(countKey, syncFailed, 1)
				continue
			}
			for _, channel := range channels {
				rtsp, err := gateway.Client.QueryChannelRealRtsp(channel.Sxjbh)
				if err != nil {
					syncLogger.Appendf("[%v] query channel [%v] rtsp failed: %v", code, channel.Tdxh, err.Error())
					_ = cache.HIncr(countKey, syncFailed, 1)
					continue
				}
				err = videoDao.Save(&modules.Video{
					PlatformId:     platform.Id,
					OrganizeNumber: detail.Zzbh,
					Name:           channel.Tdmc,
					Manufacturer:   detail.Sbcs,
					Type:           channel.Tdlx,
					GbCode:         channel.Sxjbh,
					Ip:             detail.Sbip,
					Port:           detail.Sbdkh,
					Username:       detail.Sbyhm,
					Password:       detail.Sbmm,
					Rtsp:           rtsp,
					Status:         channel.Zxzt,
					Sbxh:           detail.Sbxh,
					Tdxh:           channel.Tdxh,
				})
				if err != nil {
					syncLogger.Appendf("[%v] save video [%v] failed: %v", code, channel.Tdxh, err.Error())
					_ = cache.HIncr(countKey, syncFailed, 1)
					continue
				}
				syncLogger.Appendf("[%v] device [%v] save video [%v] is done.", code, device.Sbxh, channel.Tdxh)
				_ = cache.HIncr(countKey, syncSuccess, 1)
			}
		}
		syncLogger.Appendf("[%v] page [%v] is done.", code, page)
		if page >= pageResp.PageCount {
			break
		}
		page++
	}
	_ = cache.Expire(countKey, time.Hour*24)
	syncLogger.Appendf("[%v] is over, sync pages [%v]", code, page)
	_ = cache.Set(fmt.Sprintf(syncEndKey, code), misc.NowInMillis(), time.Hour*24)
}

func syncPlat(code string, ptxh int64, platMap map[int64]*modules.Platform) *modules.Platform {
	var platform *modules.Platform
	var ok bool
	if platform, ok = platMap[ptxh]; !ok {
		plat, err := gateway.Client.QueryPlatDetail(ptxh)
		if err != nil {
			platMap[ptxh] = platform
			syncLogger.Appendf("[%v] query plat [%v] detail failed: %v", code, ptxh, err.Error())
			return platform
		}
		if plat == nil {
			platMap[ptxh] = platform
			syncLogger.Appendf("[%v] query plat [%v] detail is nil", code, ptxh)
			return platform
		}
		platform = &modules.Platform{
			Name:         plat.Ptmc,
			Code:         plat.Ptbh,
			Manufacturer: plat.Ptcs,
			Ip:           plat.Ptip,
			Port:         plat.Ptdkh,
			Username:     plat.Ptyhm,
			Password:     plat.Ptmm,
			Ptxh:         plat.Ptxh,
		}
		err = platformDao.Save(platform)
		if err != nil {
			platMap[ptxh] = platform
			syncLogger.Appendf("[%v] save plat [%v] failed: %v", code, ptxh, err.Error())
			return platform
		}
		platMap[plat.Ptxh] = platform
		go syncOrganize(code, platform)
		syncLogger.Appendf("[%v] sync plat [%v] is done.", code, ptxh)
	}
	return platform
}

func syncOrganize(code string, platform *modules.Platform) {
	ptxh := platform.Ptxh
	organizeTree, err := gateway.Client.QueryAllOrganizes(ptxh)
	if err != nil {
		syncLogger.Appendf("[%v] query plat [%v] organize tree failed: %v", code, ptxh, err.Error())
		return
	}

	root, _ := organizationDao.GetRoot()

	var processNode func(org *gateway.OrganizeQueryResult, parentId int64)
	processNode = func(org *gateway.OrganizeQueryResult, parentId int64) {
		newOrg := &iam.Organization{
			ParentId: parentId,
			Type:     iam.OrganizationTypeNormal,
			Number:   org.Zzbh,
			Name:     org.Zzmc,
		}

		if err := organizationDao.Save(newOrg); err != nil {
			syncLogger.Appendf("[%v] save plat [%v] organize [%v] failed: %v", code, ptxh, org.Zzbh, err.Error())
			return
		}

		currentOrgId := newOrg.Id

		for _, child := range org.Children {
			processNode(child, currentOrgId)
		}
	}

	for _, rootOrg := range organizeTree {
		processNode(rootOrg, root.Id)
	}
	syncLogger.Appendf("[%v] sync plat [%v] organize is done.", code, ptxh)
}
