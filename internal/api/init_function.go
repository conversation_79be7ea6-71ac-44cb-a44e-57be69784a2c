package api

import "github.com/iWay7/go-common/iam"

var uriFunctionMap = map[string]string{
	PlatformBaseURI + "/list":   "/VideoPointManage/VideoManage",
	PlatformBaseURI + "/add":    "/VideoPointManage/VideoManage/Add",
	PlatformBaseURI + "/update": "/VideoPointManage/VideoManage/Update",
	PlatformBaseURI + "/delete": "/VideoPointManage/VideoManage/Delete",

	VideoBaseURI + "/list":         "/VideoPointManage/VideoManage",
	VideoBaseURI + "/get":          "/VideoPointManage/VideoManage",
	VideoBaseURI + "/update":       "/VideoPointManage/VideoManage/Update",
	VideoBaseURI + "/listByRegion": "/VideoPointManage/VideoManage",

	SyncBaseURI + "/trigger":  "/VideoPointManage/VideoManage",
	SyncBaseURI + "/progress": "/VideoPointManage/VideoManage/Sync",

	PointBaseURI + "/list":   "/VideoPointManage/PointManage",
	PointBaseURI + "/get":    "/VideoPointManage/PointManage",
	PointBaseURI + "/add":    "/VideoPointManage/PointManage/Add",
	PointBaseURI + "/update": "/VideoPointManage/PointManage/Update",
	PointBaseURI + "/delete": "/VideoPointManage/PointManage/Delete",

	VideoAlgorithmBaseURI + "/list":   "/VideoPointManage/PointManage",
	VideoAlgorithmBaseURI + "/get":    "/VideoPointManage/PointManage",
	VideoAlgorithmBaseURI + "/save":   "/VideoPointManage/PointManage/Add",
	VideoAlgorithmBaseURI + "/delete": "/VideoPointManage/PointManage/Delete",

	PatrolTaskBaseURI + "/list":          "/PatrolTaskManage/TaskManage",
	PatrolTaskBaseURI + "/get":           "/PatrolTaskManage/TaskManage",
	PatrolTaskBaseURI + "/add":           "/PatrolTaskManage/TaskManage/Add",
	PatrolTaskBaseURI + "/update":        "/PatrolTaskManage/TaskManage/Update",
	PatrolTaskBaseURI + "/update/status": "/PatrolTaskManage/TaskManage/Update",
	PatrolTaskBaseURI + "/delete":        "/PatrolTaskManage/TaskManage/Delete",
}

var BusinessFunctions = []iam.Function{
	{
		Code:   "VideoPointManage",
		Name:   "视频点位",
		NoPage: true,
		Children: []iam.Function{
			{
				Code:   "PointManage",
				Name:   "点位管理",
				NoPage: true,
				Children: []iam.Function{
					{Code: "Add", Name: "新增", Type: iam.FunctionTypeElement, IsOperation: true},
					{Code: "Delete", Name: "删除", Type: iam.FunctionTypeElement, IsOperation: true},
					{Code: "Update", Name: "修改", Type: iam.FunctionTypeElement, IsOperation: true},
				},
			},
			{
				Code:   "VideoManage",
				Name:   "视频资源",
				NoPage: true,
				Children: []iam.Function{
					{Code: "Add", Name: "新增", Type: iam.FunctionTypeElement, IsOperation: true},
					{Code: "Delete", Name: "删除", Type: iam.FunctionTypeElement, IsOperation: true},
					{Code: "Update", Name: "修改", Type: iam.FunctionTypeElement, IsOperation: true},
				},
			},
		},
	},
	{
		Code:   "PatrolTaskManage",
		Name:   "巡逻任务",
		NoPage: true,
		Children: []iam.Function{
			{
				Code:   "TaskManage",
				Name:   "巡逻任务",
				NoPage: true,
				Children: []iam.Function{
					{Code: "Add", Name: "新增", Type: iam.FunctionTypeElement, IsOperation: true},
					{Code: "Delete", Name: "删除", Type: iam.FunctionTypeElement, IsOperation: true},
					{Code: "Update", Name: "修改", Type: iam.FunctionTypeElement, IsOperation: true},
				},
			},
		},
	},
}
