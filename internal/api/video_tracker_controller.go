package api

import (
	"fmt"
	"net/http"
	"reflect"
	"stvpp/internal/algorithm"

	"github.com/iWay7/go-common/router"
)

type VideoTrackerController struct {
}

const VideoTrackerBaseURI = "/videoTracker"

func (controller *VideoTrackerController) Routes() []router.Route {
	return []router.Route{
		{
			Method:        http.MethodPost,
			URI:           VideoTrackerBaseURI + "/control",
			Func:          controller.Control,
			OperationType: router.OperationTypeUpdate,
			OperationName: "控制视频追踪器",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(ControlTrackerReq{}),
		},
	}
}

type ControlTrackerReq struct {
	CID    string                   `json:"cid" valid:"not_empty"`
	Action *algorithm.TrackerAction `json:"action"`
}

func (controller *VideoTrackerController) Control(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*ControlTrackerReq)
	cid := req.CID
	action := req.Action
	if action == nil {
		return router.NewFailResponse(router.ErrInvalidRequestParam, "action cannot be empty")
	}
	trackerManager := algorithm.GetTrackerManager()
	videoTracker := trackerManager.GetTrackerInfo(cid)
	if videoTracker == nil {
		return router.NewFailResponse(router.ErrDataNotFound, fmt.Sprintf("tracker for cid '%s' not found", cid))
	}
	err := action.Function(videoTracker)
	if err != nil {
		return router.NewFailResponse(router.ErrOperationFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}
