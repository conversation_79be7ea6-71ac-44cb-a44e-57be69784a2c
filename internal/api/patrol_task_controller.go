package api

import (
	"net/http"
	"reflect"
	"stvpp/internal/common"
	"stvpp/internal/modules"

	"github.com/iWay7/go-common/router"
)

type PatrolTaskController struct {
}

const PatrolTaskBaseURI = "/patrolTask"

func (controller *PatrolTaskController) Routes() []router.Route {
	return []router.Route{
		{
			Method:             http.MethodPost,
			URI:                PatrolTaskBaseURI + "/list",
			Func:               controller.ListPatrolTasks,
			OperationType:      router.OperationTypeView,
			OperationName:      "查询巡逻任务列表",
			RequireBody:        router.BodyTypeJson,
			RequestType:        reflect.TypeOf(ListPatrolTaskReq{}),
			ResponseResultType: reflect.TypeOf([]ListPatrolTaskResp{}),
		},
		{
			Method:             http.MethodPost,
			URI:                PatrolTaskBaseURI + "/get",
			Func:               controller.GetPatrolTaskInfo,
			OperationType:      router.OperationTypeView,
			OperationName:      "查询巡逻任务信息",
			RequireBody:        router.BodyTypeJson,
			RequestType:        reflect.TypeOf(GetPatrolTaskInfoReq{}),
			ResponseResultType: reflect.TypeOf(GetPatrolTaskInfoResp{}),
		},
		{
			Method:        http.MethodPost,
			URI:           PatrolTaskBaseURI + "/add",
			Func:          controller.AddPatrolTask,
			OperationType: router.OperationTypeAdd,
			OperationName: "添加巡逻任务信息",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(AddPatrolTaskReq{}),
		},
		{
			Method:        http.MethodPost,
			URI:           PatrolTaskBaseURI + "/update",
			Func:          controller.UpdatePatrolTask,
			OperationType: router.OperationTypeUpdate,
			OperationName: "修改巡逻任务信息",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(UpdatePatrolTaskReq{}),
		},
		{
			Method:        http.MethodPost,
			URI:           PatrolTaskBaseURI + "/update/status",
			Func:          controller.UpdatePatrolTaskStatus,
			OperationType: router.OperationTypeUpdate,
			OperationName: "启用/禁用巡逻任务",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(UpdatePatrolTaskStatusReq{}),
		},
		{
			Method:        http.MethodPost,
			URI:           PatrolTaskBaseURI + "/delete",
			Func:          controller.DeletePatrolTask,
			OperationType: router.OperationTypeDelete,
			OperationName: "删除巡逻任务信息",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(DeletePatrolTaskReq{}),
		},
	}
}

type ListPatrolTaskReq struct {
	Name      string `json:"name"`
	PointName string `json:"point_name"`
}

type ListPatrolTaskResp struct {
	Id            int64  `json:"id"`
	Name          string `json:"name"`
	Status        bool   `json:"status"`
	Description   string `json:"description"`
	PointCount    int64  `json:"point_count"`
	ScheduleType  int    `json:"schedule_type"`
	ScheduleCount int64  `json:"schedule_count"`
	CreatedAt     int64  `json:"created_at"`
}

func (controller *PatrolTaskController) ListPatrolTasks(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*ListPatrolTaskReq)
	tasks, err := patrolTaskDao.FindByFilter(req.Name, req.PointName)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	var list []*ListPatrolTaskResp
	for _, task := range tasks {
		list = append(list, &ListPatrolTaskResp{
			Id:            task.Id,
			Name:          task.Name,
			Status:        task.Status,
			Description:   task.Description,
			PointCount:    patrolTaskDao.CountPointByTaskIdSafely(task.Id),
			ScheduleType:  task.ScheduleType,
			ScheduleCount: patrolTaskDao.CountScheduleByTaskIdSafely(task.Id),
			CreatedAt:     task.CreatedAt,
		})
	}
	return router.NewSuccessResponse(list)
}

type GetPatrolTaskInfoReq struct {
	Id int64 `json:"id" valid:"positive"`
}

type GetPatrolTaskInfoResp struct {
	Id           int64                        `json:"id"`
	Name         string                       `json:"name"`
	Status       bool                         `json:"status"`
	Description  string                       `json:"description"`
	ScheduleType int                          `json:"schedule_type"`
	PointIds     []int64                      `json:"point_ids"`
	Schedules    []*GetPatrolTaskScheduleResp `json:"schedules"`
}

type GetPatrolTaskScheduleResp struct {
	Id       int64             `json:"id"`
	AlgoType int               `json:"algo_type"`
	Periods  []*modules.Period `json:"periods"`
}

func (controller *PatrolTaskController) GetPatrolTaskInfo(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*GetPatrolTaskInfoReq)
	task, err := patrolTaskDao.GetById(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	if task == nil {
		return router.NewSimpleFailResponse(router.ErrDataNotFound)
	}
	relations, err := patrolTaskDao.FindRelationsByTaskId(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	var pointIds []int64
	for _, relation := range relations {
		pointIds = append(pointIds, relation.PointId)
	}
	schedules, err := patrolTaskDao.FindSchedulesByTaskId(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	schedulesResp := make([]*GetPatrolTaskScheduleResp, 0, len(schedules))
	for _, schedule := range schedules {
		schedulesResp = append(schedulesResp, &GetPatrolTaskScheduleResp{
			Id:       schedule.Id,
			AlgoType: int(schedule.AlgoType),
			Periods:  schedule.Periods,
		})
	}
	return router.NewSuccessResponse(&GetPatrolTaskInfoResp{
		Id:           task.Id,
		Name:         task.Name,
		Status:       task.Status,
		Description:  task.Description,
		ScheduleType: task.ScheduleType,
		PointIds:     pointIds,
		Schedules:    schedulesResp,
	})
}

type AddPatrolTaskReq struct {
	Name         string                          `json:"name" valid:"not_empty"`
	Description  string                          `json:"description"`
	ScheduleType int                             `json:"schedule_type" valid:"positive"`
	PointIds     []int64                         `json:"point_ids"`
	Schedules    []*AddPatrolTaskScheduleReq     `json:"schedules"`
	Congestion   *modules.CongestionEarlyWarning `json:"congestion" algo:"congestion"`
	Retrograde   *modules.RetrogradeEarlyWarning `json:"retrograde" algo:"retrograde"`
	CenterStop   *modules.CenterStopEarlyWarning `json:"center_stop" algo:"center_stop"`
	Intrusion    *modules.IntrusionEarlyWarning  `json:"intrusion" algo:"intrusion"`
}

type AddPatrolTaskScheduleReq struct {
	AlgoType common.AlgoType   `json:"algo_type"`
	Periods  []*modules.Period `json:"periods" valid:"not_empty"`
}

func (controller *PatrolTaskController) AddPatrolTask(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*AddPatrolTaskReq)
	relations := make([]*modules.TaskPointRelation, 0, len(req.PointIds))
	for _, pointId := range req.PointIds {
		relations = append(relations, &modules.TaskPointRelation{
			PointId: pointId,
		})
	}
	schedules := make([]*modules.TaskSchedule, 0, len(req.Schedules))
	for _, schedule := range req.Schedules {
		schedules = append(schedules, &modules.TaskSchedule{
			AlgoType: schedule.AlgoType,
			Periods:  schedule.Periods,
		})
	}
	task := &modules.PatrolTaskConfig{
		Name:         req.Name,
		Description:  req.Description,
		ScheduleType: req.ScheduleType,
		Schedules:    schedules,
		Relations:    relations,
	}
	modules.GetEWCPUtil().CopyField(req, task, func(fieldValue interface{}, provider modules.EarlyWarningConfigProvider) interface{} {
		return fieldValue
	})
	err := patrolTaskDao.Add(task)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}

type UpdatePatrolTaskReq struct {
	Id           int64                           `json:"id" valid:"positive"`
	Name         string                          `json:"name" valid:"not_empty"`
	Description  string                          `json:"description"`
	ScheduleType int                             `json:"schedule_type" valid:"positive"`
	PointIds     []int64                         `json:"point_ids"`
	Schedules    []*UpdatePatrolTaskScheduleReq  `json:"schedules"`
	Congestion   *modules.CongestionEarlyWarning `json:"congestion" algo:"congestion"`
	Retrograde   *modules.RetrogradeEarlyWarning `json:"retrograde" algo:"retrograde"`
	CenterStop   *modules.CenterStopEarlyWarning `json:"center_stop" algo:"center_stop"`
	Intrusion    *modules.IntrusionEarlyWarning  `json:"intrusion" algo:"intrusion"`
}

type UpdatePatrolTaskScheduleReq struct {
	Id       int64             `json:"id" valid:"positive"`
	AlgoType common.AlgoType   `json:"algo_type"`
	Periods  []*modules.Period `json:"periods" valid:"not_empty"`
}

func (controller *PatrolTaskController) UpdatePatrolTask(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*UpdatePatrolTaskReq)
	relations := make([]*modules.TaskPointRelation, 0, len(req.PointIds))
	for _, pointId := range req.PointIds {
		relations = append(relations, &modules.TaskPointRelation{
			PointId: pointId,
		})
	}
	schedules := make([]*modules.TaskSchedule, 0, len(req.Schedules))
	for _, schedule := range req.Schedules {
		schedules = append(schedules, &modules.TaskSchedule{
			AlgoType: schedule.AlgoType,
			Periods:  schedule.Periods,
		})
	}
	task := &modules.PatrolTaskConfig{
		Id:          req.Id,
		Name:        req.Name,
		Description: req.Description,
		Schedules:   schedules,
		Relations:   relations,
	}
	err := patrolTaskDao.Update(task)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}

	modules.GetEWCPUtil().CopyField(req, task, func(fieldValue interface{}, provider modules.EarlyWarningConfigProvider) interface{} {
		return fieldValue
	})
	return router.NewSimpleSuccessResponse()
}

type UpdatePatrolTaskStatusReq struct {
	Id     int64 `json:"id" valid:"positive"`
	Status bool  `json:"status"`
}

func (controller *PatrolTaskController) UpdatePatrolTaskStatus(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*UpdatePatrolTaskStatusReq)
	err := patrolTaskDao.UpdateStatus(req.Id, req.Status)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}

type DeletePatrolTaskReq struct {
	Id int64 `json:"id" valid:"positive"`
}

func (controller *PatrolTaskController) DeletePatrolTask(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*DeletePatrolTaskReq)
	err := patrolTaskDao.Delete(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}
