package api

import (
	"net/http"
	"reflect"
	"stvpp/internal/modules"

	"github.com/iWay7/go-common/router"
)

type VideoAlgorithmController struct {
}

var acpUtil = modules.GetACPUtil()

const VideoAlgorithmBaseURI = "/videoAlgorithm"

func (controller *VideoAlgorithmController) Routes() []router.Route {
	return []router.Route{
		{
			Method:             http.MethodPost,
			URI:                VideoAlgorithmBaseURI + "/list",
			Func:               controller.ListVideoAlgorithmConfigs,
			OperationType:      router.OperationTypeView,
			OperationName:      "查询视频算法配置列表",
			RequireBody:        router.BodyTypeJson,
			RequestType:        reflect.TypeOf(ListVideoAlgorithmConfigsReq{}),
			ResponseResultType: reflect.TypeOf([]ListVideoAlgorithmConfigsResp{}),
		},
		{
			Method:             http.MethodPost,
			URI:                VideoAlgorithmBaseURI + "/get",
			Func:               controller.GetVideoAlgorithmConfig,
			OperationType:      router.OperationTypeView,
			OperationName:      "查询视频算法配置详情",
			RequireBody:        router.BodyTypeJson,
			RequestType:        reflect.TypeOf(GetVideoAlgorithmConfigReq{}),
			ResponseResultType: reflect.TypeOf([]GetVideoAlgorithmConfigResp{}),
		},
		{
			Method:        http.MethodPost,
			URI:           VideoAlgorithmBaseURI + "/save",
			Func:          controller.SaveVideoAlgorithm,
			OperationType: router.OperationTypeAdd,
			OperationName: "保存视频算法配置",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(SaveVideoAlgorithmReq{}),
		},
		{
			Method:        http.MethodPost,
			URI:           VideoAlgorithmBaseURI + "/delete",
			Func:          controller.DeleteVideoAlgorithm,
			OperationType: router.OperationTypeDelete,
			OperationName: "删除视频算法配置",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(DeleteVideoAlgorithmReq{}),
		},
	}
}

type ListVideoAlgorithmConfigsReq struct {
	VideoId int64 `json:"video_id" valid:"positive"`
}

type ListVideoAlgorithmConfigsResp struct {
	Id               int64                       `json:"id"`
	Name             string                      `json:"name"`
	GbCode           string                      `json:"gb_code"`
	PlatformName     string                      `json:"platform_name"`
	OrganizeName     string                      `json:"organize_name"`
	Status           int                         `json:"status"`
	Description      string                      `json:"description"`
	Longitude        float64                     `json:"longitude"`
	Latitude         float64                     `json:"latitude"`
	AlgorithmConfigs []*ListAlgorithmConfigsResp `json:"algorithm_configs,omitempty"`
}

type ListAlgorithmConfigsResp struct {
	Id         int64  `json:"id"`
	Name       string `json:"name"`
	Congestion bool   `json:"congestion" algo:"congestion"`
	Retrograde bool   `json:"retrograde" algo:"retrograde"`
	CenterStop bool   `json:"center_stop" algo:"center_stop"`
	Intrusion  bool   `json:"intrusion" algo:"intrusion"`
}

func (controller *VideoAlgorithmController) ListVideoAlgorithmConfigs(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*ListVideoAlgorithmConfigsReq)
	video, err := videoDao.GetById(req.VideoId)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	if video == nil {
		return router.NewSimpleFailResponse(router.ErrDataNotFound)
	}

	regions, err := regionDao.FindByVideoId(req.VideoId)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	var configs []*ListAlgorithmConfigsResp
	for _, r := range regions {
		cfg := &ListAlgorithmConfigsResp{
			Id:   r.Id,
			Name: r.Name,
		}
		configs = append(configs, cfg)
		acpUtil.SetField(cfg, func(provider modules.AlgorithmConfigProvider) interface{} {
			return provider.ExistsByArIdAndVIdSafely(cfg.Id, req.VideoId)
		})
	}
	return router.NewSuccessResponse(&ListVideoAlgorithmConfigsResp{
		Id:               video.Id,
		Name:             video.Name,
		GbCode:           video.GbCode,
		PlatformName:     platformDao.GetNameByIdSafely(video.PlatformId),
		OrganizeName:     organizationDao.GetNameByNumberSafely(video.OrganizeNumber),
		Status:           video.Status,
		Description:      video.Description,
		Longitude:        video.Longitude,
		Latitude:         video.Latitude,
		AlgorithmConfigs: configs,
	})
}

type GetVideoAlgorithmConfigReq struct {
	AlgorithmConfigId int64 `json:"algorithm_config_id" valid:"positive"`
}

type GetVideoAlgorithmConfigResp struct {
	Id            int64                     `json:"id"`
	VideoId       int64                     `json:"video_id"`
	MarkRegion    *modules.GeomPolygon      `json:"mark_region"`
	Name          string                    `json:"name"`
	Direction     int                       `json:"direction"`
	LaneDirection int                       `json:"lane_direction"`
	LaneType      int                       `json:"lane_type"`
	LaneNumber    int                       `json:"lane_number"`
	Congestion    *modules.CongestionConfig `json:"congestion,omitempty" algo:"congestion"`
	Retrograde    *modules.RetrogradeConfig `json:"retrograde,omitempty" algo:"retrograde"`
	CenterStop    *modules.CenterStopConfig `json:"center_stop,omitempty" algo:"center_stop"`
	Intrusion     *modules.IntrusionConfig  `json:"intrusion,omitempty" algo:"intrusion"`
}

func (controller *VideoAlgorithmController) GetVideoAlgorithmConfig(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*GetVideoAlgorithmConfigReq)
	ars, err := regionDao.FindByVideoId(req.AlgorithmConfigId)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}

	var list []*GetVideoAlgorithmConfigResp
	for _, ar := range ars {
		resp := &GetVideoAlgorithmConfigResp{
			Id:            ar.Id,
			VideoId:       ar.VideoId,
			MarkRegion:    ar.MarkRegion,
			Name:          ar.Name,
			Direction:     ar.Direction,
			LaneDirection: ar.LaneDirection,
			LaneType:      ar.LaneType,
			LaneNumber:    ar.LaneNumber,
		}
		acpUtil.SetField(resp, func(provider modules.AlgorithmConfigProvider) interface{} {
			return provider.GetByAlgorithmRegionIdSafely(ar.Id)
		})
		list = append(list, resp)
	}
	return router.NewSuccessResponse(list)
}

type SaveVideoAlgorithmReq struct {
	Id            int64                     `json:"id"`
	VideoId       int64                     `json:"video_id"`
	MarkRegion    *modules.GeomPolygon      `json:"mark_region"`
	Name          string                    `json:"name"`
	Direction     int                       `json:"direction"`
	LaneDirection int                       `json:"lane_direction"`
	LaneType      int                       `json:"lane_type"`
	LaneNumber    int                       `json:"lane_number"`
	Congestion    *modules.CongestionConfig `json:"congestion" algo:"congestion"`
	Retrograde    *modules.RetrogradeConfig `json:"retrograde" algo:"retrograde"`
	CenterStop    *modules.CenterStopConfig `json:"center_stop" algo:"center_stop"`
	Intrusion     *modules.IntrusionConfig  `json:"intrusion" algo:"intrusion"`
}

func (controller *VideoAlgorithmController) SaveVideoAlgorithm(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*SaveVideoAlgorithmReq)
	ac := &modules.VideoAlgorithmConfig{
		Region: &modules.Region{
			Id:            req.Id,
			VideoId:       req.VideoId,
			MarkRegion:    req.MarkRegion,
			Name:          req.Name,
			Direction:     req.Direction,
			LaneDirection: req.LaneDirection,
			LaneType:      req.LaneType,
			LaneNumber:    req.LaneNumber,
		},
	}

	acpUtil.CopyField(req, ac, func(fv interface{}, provider modules.AlgorithmConfigProvider) interface{} {
		return provider.ConfigToModule(fv)
	})
	err := regionDao.Save(ac)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}

type DeleteVideoAlgorithmReq struct {
	Id int64 `json:"id" valid:"positive"`
}

func (controller *VideoAlgorithmController) DeleteVideoAlgorithm(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*DeleteVideoAlgorithmReq)
	err := regionDao.Delete(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}
