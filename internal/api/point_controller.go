package api

import (
	"net/http"
	"reflect"
	"stvpp/internal/modules"

	"github.com/iWay7/go-common/router"
)

type PointController struct {
}

const PointBaseURI = "/point"

func (controller *PointController) Routes() []router.Route {
	return []router.Route{
		{
			Method:             http.MethodPost,
			URI:                PointBaseURI + "/list",
			Func:               controller.ListPoints,
			OperationType:      router.OperationTypeView,
			OperationName:      "查询视频点位列表",
			RequireBody:        router.BodyTypeJson,
			RequestType:        reflect.TypeOf(ListPointReq{}),
			ResponseResultType: reflect.TypeOf(PageResult[ListPointResp]{}),
		},
		{
			Method:             http.MethodPost,
			URI:                PointBaseURI + "/get",
			Func:               controller.GetPoint,
			OperationType:      router.OperationTypeView,
			OperationName:      "查询视频点位信息",
			RequireBody:        router.BodyTypeJson,
			RequestType:        reflect.TypeOf(GetPointReq{}),
			ResponseResultType: reflect.TypeOf(GetPointResp{}),
		},
		{
			Method:        http.MethodPost,
			URI:           PointBaseURI + "/add",
			Func:          controller.AddPoint,
			OperationType: router.OperationTypeAdd,
			OperationName: "添加视频点位信息",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(AddPointReq{}),
		},
		{
			Method:        http.MethodPost,
			URI:           PointBaseURI + "/update",
			Func:          controller.UpdatePoint,
			OperationType: router.OperationTypeUpdate,
			OperationName: "修改视频点位信息",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(UpdatePointReq{}),
		},
		{
			Method:        http.MethodPost,
			URI:           PointBaseURI + "/delete",
			Func:          controller.DeletePatrolRegion,
			OperationType: router.OperationTypeDelete,
			OperationName: "删除视频点位信息",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(DeletePointReq{}),
		},
	}
}

type ListPointReq struct {
	Name      string `json:"name"`
	VideoCode string `json:"video_code"`
}

type ListPointResp struct {
	Id          int64                 `json:"id"`
	Name        string                `json:"name"`
	Type        int                   `json:"type"`
	SecType     int                   `json:"sec_type"`
	Area        string                `json:"area"`
	Key         bool                  `json:"key"`
	ImgPath     string                `json:"img_path"`
	Boundary    *modules.GeomPolygon  `json:"boundary"`
	Description string                `json:"description"`
	Videos      []*ListPointVideoResp `json:"videos"`
}

type ListPointVideoResp struct {
	Id     int64              `json:"id"`
	Name   string             `json:"name"`
	GbCode string             `json:"gb_code"`
	Status int                `json:"status"`
	Point  *modules.GeomPoint `json:"point"`
}

func (controller *PointController) ListPoints(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*ListPointReq)
	points, err := pointDao.FindByFilter(req.Name, req.VideoCode)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	var list []*ListPointResp
	for _, point := range points {
		relationsMap, videoIds, err := pointDao.FindRelationMapById(point.Id)
		if err != nil {
			return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
		}
		videos, err := videoDao.FindInIds(videoIds)
		if err != nil {
			return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
		}
		var videosResp []*ListPointVideoResp
		for _, video := range videos {
			videosResp = append(videosResp, &ListPointVideoResp{
				Id:     video.Id,
				Name:   video.Name,
				GbCode: video.GbCode,
				Status: video.Status,
				Point:  relationsMap[video.Id].Point,
			})
		}
		list = append(list, &ListPointResp{
			Id:          point.Id,
			Name:        point.Name,
			Type:        point.Type,
			SecType:     point.SecType,
			Area:        point.Area,
			Key:         point.Key,
			ImgPath:     point.ImgPath,
			Boundary:    point.Boundary,
			Description: point.Description,
			Videos:      videosResp,
		})
	}
	return router.NewSuccessResponse(list)
}

type GetPointReq struct {
	Id int64 `json:"id" valid:"positive"`
}

type GetPointResp struct {
	Id          int64                `json:"id"`
	Name        string               `json:"name"`
	Type        int                  `json:"type"`
	SecType     int                  `json:"sec_type"`
	Area        string               `json:"area"`
	Key         bool                 `json:"key"`
	ImgPath     string               `json:"img_path"`
	Boundary    *modules.GeomPolygon `json:"boundary"`
	Description string               `json:"description"`
	Videos      []*GetPointVideoResp `json:"videos"`
}
type GetPointVideoResp struct {
	Id     int64              `json:"id"`
	Name   string             `json:"name"`
	GbCode string             `json:"gb_code"`
	Status int                `json:"status"`
	Point  *modules.GeomPoint `json:"point"`
}

func (controller *PointController) GetPoint(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*GetPointReq)
	point, err := pointDao.GetById(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	if point == nil {
		return router.NewSimpleFailResponse(router.ErrDataNotFound)
	}
	relationMap, videoIds, err := pointDao.FindRelationMapById(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	videos, err := videoDao.FindInIds(videoIds)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	var videosResp []*GetPointVideoResp
	for _, video := range videos {
		videosResp = append(videosResp, &GetPointVideoResp{
			Id:     video.Id,
			Name:   video.Name,
			GbCode: video.GbCode,
			Status: video.Status,
			Point:  relationMap[video.Id].Point,
		})
	}
	return router.NewSuccessResponse(&GetPointResp{
		Id:          point.Id,
		Name:        point.Name,
		Type:        point.Type,
		SecType:     point.SecType,
		Area:        point.Area,
		Key:         point.Key,
		ImgPath:     point.ImgPath,
		Boundary:    point.Boundary,
		Description: point.Description,
		Videos:      videosResp,
	})
}

type AddPointReq struct {
	Name        string               `json:"name" valid:"not_empty"`
	Type        int                  `json:"type" valid:"positive"`
	SecType     int                  `json:"sec_type" valid:"positive"`
	Area        string               `json:"area" valid:"not_empty"`
	Key         bool                 `json:"key" valid:"not_empty"`
	ImgPath     string               `json:"img_path" valid:"not_empty"`
	Boundary    *modules.GeomPolygon `json:"boundary" valid:"not_empty"`
	Description string               `json:"description"`
	Videos      []*AddPointVideoReq  `json:"videos"`
}

type AddPointVideoReq struct {
	VideoId int64              `json:"video_id" valid:"positive"`
	Point   *modules.GeomPoint `json:"point" valid:"not_empty"`
}

func (controller *PointController) AddPoint(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*AddPointReq)
	relations := make([]*modules.PointVideoRelation, 0, len(req.Videos))
	for _, video := range req.Videos {
		relations = append(relations, &modules.PointVideoRelation{
			VideoId: video.VideoId,
			Point:   video.Point,
		})
	}
	err := pointDao.Add(&modules.Point{
		Name:        req.Name,
		Type:        req.Type,
		SecType:     req.SecType,
		Area:        req.Area,
		Key:         req.Key,
		ImgPath:     req.ImgPath,
		Boundary:    req.Boundary,
		Description: req.Description,
		Relations:   relations,
	})
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}

type UpdatePointReq struct {
	Id          int64                  `json:"id" valid:"positive"`
	Name        string                 `json:"name" valid:"not_empty"`
	Type        int                    `json:"type" valid:"positive"`
	SecType     int                    `json:"sec_type" valid:"positive"`
	Area        string                 `json:"area" valid:"not_empty"`
	Key         bool                   `json:"key" valid:"not_empty"`
	ImgPath     string                 `json:"img_path" valid:"not_empty"`
	Boundary    *modules.GeomPolygon   `json:"boundary" valid:"not_empty"`
	Description string                 `json:"description"`
	Videos      []*UpdatePointVideoReq `json:"videos"`
}

type UpdatePointVideoReq struct {
	Id      int64              `json:"id"`
	VideoId int64              `json:"video_id" valid:"positive"`
	Point   *modules.GeomPoint `json:"point" valid:"not_empty"`
}

func (controller *PointController) UpdatePoint(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*UpdatePointReq)
	relations := make([]*modules.PointVideoRelation, 0, len(req.Videos))
	for _, video := range req.Videos {
		relations = append(relations, &modules.PointVideoRelation{
			Id:      video.Id,
			VideoId: video.VideoId,
			Point:   video.Point,
		})
	}
	err := pointDao.Update(&modules.Point{
		Id:          req.Id,
		Name:        req.Name,
		Type:        req.Type,
		SecType:     req.SecType,
		Area:        req.Area,
		Key:         req.Key,
		ImgPath:     req.ImgPath,
		Boundary:    req.Boundary,
		Description: req.Description,
		Relations:   relations,
	})
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}

type DeletePointReq struct {
	Id int64 `json:"id" valid:"positive"`
}

func (controller *PointController) DeletePatrolRegion(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*DeletePointReq)
	err := pointDao.Delete(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}
