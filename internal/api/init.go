package api

import (
	"stvpp/internal/modules"

	"github.com/chai/create-api/gen"
	"github.com/iWay7/go-common/iam"
	"github.com/iWay7/go-common/router"
)

const (
	ErrCallGatewayService = "CallGatewayServiceFailed:调用视频接入网关失败"
	ErrSyncIsNotStarted   = "SyncIsNotStarted:同步任务尚未启动"
)

var (
	platformDao         *modules.PlatformDao
	videoDao            *modules.VideoDao
	patrolTaskDao       *modules.PatrolTaskDao
	pointDao            *modules.PointDao
	regionDao           *modules.RegionDao
	videoTrackerStatDao *modules.VideoTrackerStatDao
	organizationDao     *iam.OrganizationDao
)

type PageParam struct {
	PageIndex int `json:"page_index" valid:"positive"`
	PageSize  int `json:"page_size" valid:"positive max(100)"`
}

type PageResult[T interface{}] struct {
	PageSize   int   `json:"page_size"`
	PageIndex  int   `json:"page_index"`
	TotalCount int64 `json:"total_count"`
	PageCount  int64 `json:"page_count"`
	List       []*T  `json:"list"`
}

func Init() {
	platformDao = modules.GetPlatformDao()
	videoDao = modules.GetVideoDao()
	patrolTaskDao = modules.GetPatrolTaskDao()
	pointDao = modules.GetPointDao()
	regionDao = modules.GetRegionDao()
	videoTrackerStatDao = modules.GetVideoTrackerStatDao()
	organizationDao = iam.GetOrganizationDao()

	type Controller interface {
		Routes() []router.Route
	}
	registerController := func(controller Controller) {
		gen.RegisterApi(controller)
		routes := controller.Routes()
		for i := 0; i < len(routes); i++ {
			// routes[i].FunctionPath = uriFunctionMap[routes[i].URI]
			router.RegisterRoute(routes[i])
		}
	}

	registerController(new(PatrolTaskController))
	registerController(new(PlatformController))
	registerController(new(PointController))
	registerController(new(SyncController))
	registerController(new(VideoAlgorithmController))
	registerController(new(VideoController))
	registerController(new(VideoTrackerController))
}
