package api

import (
	"net/http"
	"reflect"
	"stvpp/internal/gateway"
	"stvpp/internal/modules"

	"github.com/iWay7/go-common/router"
)

type PlatformController struct {
}

const PlatformBaseURI = "/platform"

func (controller *PlatformController) Routes() []router.Route {
	return []router.Route{
		{
			Method:             http.MethodPost,
			URI:                PlatformBaseURI + "/list",
			Func:               controller.ListPlatforms,
			OperationType:      router.OperationTypeView,
			OperationName:      "查询平台列表",
			RequireBody:        router.BodyTypeAny,
			ResponseResultType: reflect.TypeOf([]modules.Platform{}),
		},
		{
			Method:        http.MethodPost,
			URI:           PlatformBaseURI + "/add",
			Func:          controller.AddPlatform,
			OperationType: router.OperationTypeAdd,
			OperationName: "添加平台信息",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(AddPlatformReq{}),
		},
		{
			Method:        http.MethodPost,
			URI:           PlatformBaseURI + "/update",
			Func:          controller.UpdatePlatform,
			OperationType: router.OperationTypeUpdate,
			OperationName: "修改平台信息",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(UpdatePlatformReq{}),
		},
		{
			Method:        http.MethodPost,
			URI:           PlatformBaseURI + "/delete",
			Func:          controller.DeletePlatform,
			OperationType: router.OperationTypeDelete,
			OperationName: "删除平台信息",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(DeletePlatformReq{}),
		},
	}
}

func (controller *PlatformController) Generate() (string, bool, int) {
	return "资源管理/平台管理", false, -1
}

func (controller *PlatformController) ListPlatforms(ctx *router.RequestContext) router.Response {
	platforms, err := platformDao.ListAll()
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSuccessResponse(platforms)
}

type AddPlatformReq struct {
	Name         string `json:"name" valid:"not_empty"`
	Code         string `json:"code" valid:"not_empty"`
	Manufacturer string `json:"manufacturer" valid:"not_empty"`
	Ip           string `json:"ip" valid:"not_empty"`
	Port         int    `json:"port" valid:"positive"`
	Username     string `json:"username" valid:"not_empty"`
	Password     string `json:"password" valid:"not_empty"`
	Description  string `json:"description"`
}

func (controller *PlatformController) AddPlatform(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*AddPlatformReq)
	ptxh, err := gateway.Client.AddPlat(&gateway.PlatAddReq{
		Ptbh:  req.Code,
		Ptmc:  req.Name,
		Ptcs:  req.Manufacturer,
		Ptip:  req.Ip,
		Ptdkh: req.Port,
		Ptyhm: req.Username,
		Ptmm:  req.Password,
	})
	if err != nil {
		return router.NewFailResponse(ErrCallGatewayService, err.Error())
	}
	err = platformDao.Add(&modules.Platform{
		Name:         req.Name,
		Code:         req.Code,
		Manufacturer: req.Manufacturer,
		Ip:           req.Ip,
		Port:         req.Port,
		Username:     req.Username,
		Password:     req.Password,
		Description:  req.Description,
		Ptxh:         ptxh,
	})
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}

type UpdatePlatformReq struct {
	Id           int64  `json:"id" valid:"positive"`
	Name         string `json:"name" valid:"not_empty"`
	Code         string `json:"code" valid:"not_empty"`
	Manufacturer string `json:"manufacturer" valid:"not_empty"`
	Ip           string `json:"ip" valid:"not_empty"`
	Port         int    `json:"port" valid:"positive"`
	Username     string `json:"username" valid:"not_empty"`
	Password     string `json:"password" valid:"not_empty"`
	Description  string `json:"description"`
}

func (controller *PlatformController) UpdatePlatform(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*UpdatePlatformReq)
	plat, err := platformDao.GetById(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	err = gateway.Client.EditPlat(&gateway.PlatUpdateReq{
		Ptxh:  plat.Ptxh,
		Ptbh:  req.Code,
		Ptmc:  req.Name,
		Ptcs:  req.Manufacturer,
		Ptip:  req.Ip,
		Ptdkh: req.Port,
		Ptyhm: req.Username,
		Ptmm:  req.Password,
	})
	if err != nil {
		return router.NewFailResponse(ErrCallGatewayService, err.Error())
	}
	plat.Name = req.Name
	plat.Code = req.Code
	plat.Manufacturer = req.Manufacturer
	plat.Ip = req.Ip
	plat.Port = req.Port
	plat.Username = req.Username
	plat.Password = req.Password
	plat.Description = req.Description
	err = platformDao.Update(plat)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}

type DeletePlatformReq struct {
	Id int64 `json:"id" valid:"positive"`
}

func (controller *PlatformController) DeletePlatform(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*DeletePlatformReq)
	platform, err := platformDao.GetById(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	err = gateway.Client.DeletePlat(platform.Code)
	if err != nil {
		return router.NewFailResponse(ErrCallGatewayService, err.Error())
	}
	err = platformDao.Delete(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}
