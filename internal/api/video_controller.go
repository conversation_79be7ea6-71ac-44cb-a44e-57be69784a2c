package api

import (
	"net/http"
	"reflect"
	"stvpp/internal/gateway"
	"stvpp/internal/modules"

	"github.com/iWay7/go-common/misc"
	"github.com/iWay7/go-common/router"
)

type VideoController struct {
}

const VideoBaseURI = "/video"

func (controller *VideoController) Routes() []router.Route {
	return []router.Route{
		{
			Method:             http.MethodPost,
			URI:                VideoBaseURI + "/list",
			Func:               controller.ListVideos,
			OperationType:      router.OperationTypeView,
			OperationName:      "查询视频列表",
			RequireBody:        router.BodyTypeJson,
			RequestType:        reflect.TypeOf(ListVideosReq{}),
			ResponseResultType: reflect.TypeOf(PageResult[ListVideosResp]{}),
		},
		{
			Method:             http.MethodPost,
			URI:                VideoBaseURI + "/get",
			Func:               controller.GetVideoInfo,
			OperationType:      router.OperationTypeView,
			OperationName:      "查询视频信息",
			RequireBody:        router.BodyTypeJson,
			RequestType:        reflect.TypeOf(GetVideoReq{}),
			ResponseResultType: reflect.TypeOf(GetVideosResp{}),
		},
		{
			Method:        http.MethodPost,
			URI:           VideoBaseURI + "/update",
			Func:          controller.UpdateVideo,
			OperationType: router.OperationTypeUpdate,
			OperationName: "修改视频信息",
			RequireBody:   router.BodyTypeJson,
			RequestType:   reflect.TypeOf(UpdateVideoReq{}),
		},
		{
			Method:             http.MethodPost,
			URI:                VideoBaseURI + "/listByRegion",
			Func:               controller.ListVideoByRegion,
			OperationType:      router.OperationTypeView,
			OperationName:      "查询区域内的视频",
			RequireBody:        router.BodyTypeJson,
			RequestType:        reflect.TypeOf(ListVideoByRegionReq{}),
			ResponseResultType: reflect.TypeOf([]ListVideoByRegionResp{}),
		},
	}
}

type ListVideosReq struct {
	PageParam
	GbCode string `json:"gb_code"`
	Name   string `json:"name"`
	Status int    `json:"status"`
}

type ListVideosResp struct {
	Id           int64   `json:"id"`
	Name         string  `json:"name"`
	GbCode       string  `json:"gb_code"`
	PlatformName string  `json:"platform_name"`
	OrganizeName string  `json:"organize_name"`
	Status       int     `json:"status"`
	Description  string  `json:"description"`
	Longitude    float64 `json:"longitude"`
	Latitude     float64 `json:"latitude"`
}

func (controller *VideoController) ListVideos(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*ListVideosReq)
	videos, count, err := videoDao.FindByFilter(req.GbCode,
		req.Name,
		req.Status,
		misc.CalcLimit(req.PageSize, req.PageIndex),
		misc.CalcStart(req.PageSize, req.PageIndex))
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	var list []*ListVideosResp
	for _, video := range videos {
		list = append(list, &ListVideosResp{
			Id:           video.Id,
			Name:         video.Name,
			GbCode:       video.GbCode,
			PlatformName: platformDao.GetNameByIdSafely(video.PlatformId),
			OrganizeName: organizationDao.GetNameByNumberSafely(video.OrganizeNumber),
			Status:       video.Status,
			Description:  video.Description,
			Longitude:    video.Longitude,
			Latitude:     video.Latitude,
		})
	}
	return router.NewSuccessResponse(PageResult[ListVideosResp]{
		PageSize:   req.PageSize,
		PageIndex:  req.PageIndex,
		TotalCount: count,
		PageCount:  misc.CalcPageCount(req.PageSize, count),
		List:       list,
	})
}

type GetVideoReq struct {
	Id int64 `json:"id"`
}

type GetVideosResp struct {
	Id             int64   `json:"id"`
	PlatformName   string  `json:"platform_name"`
	PlatformCode   string  `json:"platform_code"`
	Name           string  `json:"name"`
	GbCode         string  `json:"gb_code"`
	Manufacturer   string  `json:"manufacturer"`
	Status         int     `json:"status"`
	Type           string  `json:"type"`
	Ip             string  `json:"ip"`
	Port           int     `json:"port"`
	Username       string  `json:"username"`
	Password       string  `json:"password"`
	OrganizeNumber string  `json:"organize_number"`
	OrganizeName   string  `json:"organize_name"`
	Longitude      float64 `json:"longitude"`
	Latitude       float64 `json:"latitude"`
	Description    string  `json:"description"`
}

func (controller *VideoController) GetVideoInfo(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*GetVideoReq)
	video, err := videoDao.GetById(req.Id)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	if video == nil {
		return router.NewSimpleFailResponse(router.ErrDataNotFound)
	}
	platform, err := platformDao.GetById(video.PlatformId)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	if platform == nil {
		return router.NewSimpleFailResponse(router.ErrDataNotFound)
	}
	organize, err := organizationDao.GetByNumber(video.OrganizeNumber)
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	if organize == nil {
		return router.NewSimpleFailResponse(router.ErrDataNotFound)
	}
	return router.NewSuccessResponse(&GetVideosResp{
		Id:             video.Id,
		PlatformName:   platform.Name,
		PlatformCode:   platform.Code,
		Name:           video.Name,
		GbCode:         video.GbCode,
		Manufacturer:   video.Manufacturer,
		Status:         video.Status,
		Type:           video.Type,
		Ip:             video.Ip,
		Port:           video.Port,
		Username:       video.Username,
		Password:       video.Password,
		OrganizeNumber: organize.Number,
		OrganizeName:   organize.Name,
		Longitude:      video.Longitude,
		Latitude:       video.Latitude,
		Description:    video.Description,
	})
}

type UpdateVideoReq struct {
	Id          int64   `json:"id" valid:"positive"`
	Name        string  `json:"name" valid:"not_empty"`
	Longitude   float64 `json:"longitude"`
	Latitude    float64 `json:"latitude"`
	Description string  `json:"description"`
}

func (controller *VideoController) UpdateVideo(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*UpdateVideoReq)
	err := gateway.Client.EditChannel(&gateway.ChannelEditReq{
		Tdxh: req.Id,
		Tdmc: req.Name,
	})
	if err != nil {
		return router.NewFailResponse(ErrCallGatewayService, err.Error())
	}
	err = videoDao.Update(&modules.Video{
		Id:          req.Id,
		Name:        req.Name,
		Longitude:   req.Longitude,
		Latitude:    req.Latitude,
		Description: req.Description,
	})
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	return router.NewSimpleSuccessResponse()
}

type ListVideoByRegionReq struct {
	PageParam
	Boundary *modules.GeomPolygon `json:"boundary" valid:"not_empty"`
	Keyword  string               `json:"keyword"`
}

type ListVideoByRegionResp struct {
	Id           int64   `json:"id"`
	Name         string  `json:"name"`
	GbCode       string  `json:"gb_code"`
	PlatformName string  `json:"platform_name"`
	Status       int     `json:"status"`
	Longitude    float64 `json:"longitude"`
	Latitude     float64 `json:"latitude"`
}

func (controller *VideoController) ListVideoByRegion(ctx *router.RequestContext) router.Response {
	req := ctx.RequestObject.(*ListVideoByRegionReq)
	videos, count, err := videoDao.FindByBoundary(req.Boundary, req.Keyword,
		misc.CalcLimit(req.PageSize, req.PageIndex),
		misc.CalcStart(req.PageSize, req.PageIndex))
	if err != nil {
		return router.NewFailResponse(router.ErrDatabaseOperateFailed, err.Error())
	}
	var list []*ListVideoByRegionResp
	for _, video := range videos {
		list = append(list, &ListVideoByRegionResp{
			Id:           video.Id,
			Name:         video.Name,
			GbCode:       video.GbCode,
			PlatformName: platformDao.GetNameByIdSafely(video.PlatformId),
			Status:       video.Status,
			Longitude:    video.Longitude,
			Latitude:     video.Latitude,
		})
	}
	return router.NewSuccessResponse(PageResult[ListVideoByRegionResp]{
		PageSize:   req.PageSize,
		PageIndex:  req.PageIndex,
		TotalCount: count,
		PageCount:  misc.CalcPageCount(req.PageSize, count),
		List:       list,
	})
}
