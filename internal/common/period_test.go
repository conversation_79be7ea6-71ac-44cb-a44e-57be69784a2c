package common

import (
	"testing"
	"time"
)

func TestPeriod_IsInPeriod(t *testing.T) {
	tests := []struct {
		name     string
		period   Period
		testTime time.Time
		want     bool
	}{
		{
			name: "周一到周五工作时间",
			period: Period{
				Keys: []int{1, 2, 3, 4, 5}, // 周一到周五
				Time: &PeriodTime{
					Start: "09:00:00",
					End:   "18:00:00",
				},
			},
			testTime: time.Date(2025, 8, 26, 14, 30, 0, 0, time.UTC), // 周二下午2:30
			want:     true,
		},
		{
			name: "周末不在工作时间",
			period: Period{
				Keys: []int{1, 2, 3, 4, 5}, // 周一到周五
				Time: &PeriodTime{
					Start: "09:00:00",
					End:   "18:00:00",
				},
			},
			testTime: time.Date(2025, 8, 24, 14, 30, 0, 0, time.UTC), // 周日下午2:30
			want:     false,
		},
		{
			name: "工作日但不在工作时间",
			period: Period{
				Keys: []int{1, 2, 3, 4, 5}, // 周一到周五
				Time: &PeriodTime{
					Start: "09:00:00",
					End:   "18:00:00",
				},
			},
			testTime: time.Date(2025, 8, 26, 20, 30, 0, 0, time.UTC), // 周二晚上8:30
			want:     false,
		},
		{
			name: "日期范围内的时间",
			period: Period{
				Range: &PeriodRange{
					Begin: "2025-08-01",
					End:   "2025-08-31",
				},
				Time: &PeriodTime{
					Start: "10:00:00",
					End:   "16:00:00",
				},
			},
			testTime: time.Date(2025, 8, 15, 12, 0, 0, 0, time.UTC), // 8月15日中午12点
			want:     true,
		},
		{
			name: "日期范围外的时间",
			period: Period{
				Range: &PeriodRange{
					Begin: "2025-08-01",
					End:   "2025-08-31",
				},
				Time: &PeriodTime{
					Start: "10:00:00",
					End:   "16:00:00",
				},
			},
			testTime: time.Date(2025, 9, 15, 12, 0, 0, 0, time.UTC), // 9月15日中午12点
			want:     false,
		},
		{
			name: "跨天时间范围 - 在范围内",
			period: Period{
				Keys: []int{1, 2, 3, 4, 5, 6, 7}, // 每天
				Time: &PeriodTime{
					Start: "22:00:00",
					End:   "06:00:00",
				},
			},
			testTime: time.Date(2025, 8, 26, 23, 30, 0, 0, time.UTC), // 晚上11:30
			want:     true,
		},
		{
			name: "跨天时间范围 - 早晨在范围内",
			period: Period{
				Keys: []int{1, 2, 3, 4, 5, 6, 7}, // 每天
				Time: &PeriodTime{
					Start: "22:00:00",
					End:   "06:00:00",
				},
			},
			testTime: time.Date(2025, 8, 26, 3, 30, 0, 0, time.UTC), // 凌晨3:30
			want:     true,
		},
		{
			name: "跨天时间范围 - 不在范围内",
			period: Period{
				Keys: []int{1, 2, 3, 4, 5, 6, 7}, // 每天
				Time: &PeriodTime{
					Start: "22:00:00",
					End:   "06:00:00",
				},
			},
			testTime: time.Date(2025, 8, 26, 12, 0, 0, 0, time.UTC), // 中午12点
			want:     false,
		},
		{
			name: "全天时间（开始和结束时间相同）",
			period: Period{
				Keys: []int{7}, // 周日
				Time: &PeriodTime{
					Start: "00:00:00",
					End:   "00:00:00",
				},
			},
			testTime: time.Date(2025, 8, 24, 15, 30, 0, 0, time.UTC), // 周日下午3:30
			want:     true,
		},
		{
			name: "Time为空",
			period: Period{
				Keys: []int{1, 2, 3, 4, 5},
				Time: nil,
			},
			testTime: time.Date(2025, 8, 26, 12, 0, 0, 0, time.UTC),
			want:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.period.IsInPeriod(tt.testTime); got != tt.want {
				t.Errorf("Period.IsInPeriod() = %v, want %v", got, tt.want)
			}
		})
	}
}


