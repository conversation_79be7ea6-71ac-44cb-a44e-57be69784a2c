package common

import (
	"net"
	"sync"

	"github.com/iWay7/go-common/logger"
)

var localIP string
var mu sync.Mutex

func GetLocalIP() string {
	mu.Lock()
	defer mu.Unlock()
	if localIP != "" {
		return localIP
	}

	interfaces, err := net.Interfaces()
	if err != nil {
		logger.Wf("无法获取网络接口: %v", err)
		localIP = "127.0.0.1"
		return localIP
	}

	for _, iface := range interfaces {
		if iface.Flags&net.FlagUp == 0 {
			continue // interface down
		}
		if iface.Flags&net.FlagLoopback != 0 {
			continue // loopback interface
		}
		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}
		for _, addr := range addrs {
			var ip net.IP
			switch v := addr.(type) {
			case *net.IPNet:
				ip = v.IP
			case *net.IPAddr:
				ip = v.IP
			}
			if ip == nil || ip.IsLoopback() {
				continue
			}
			ip = ip.To4()
			if ip == nil {
				continue // not an ipv4 address
			}
			localIP = ip.String()
			logger.If("获取到本机IP地址: %s", localIP)
			return localIP
		}
	}

	logger.Wf("无法找到有效的本机IP地址, 将使用 127.0.0.1")
	localIP = "127.0.0.1"
	return localIP
}
