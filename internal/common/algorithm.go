package common

import "reflect"

const (
	AlgorithmTag = "algo"
)

type AlgoType int

const (
	AlgoTypeAnalyse    AlgoType = iota // 态势分析
	AlgoTypeCongestion                 // 拥堵检测
	AlgoTypeRetrograde                 // 逆行检测
	AlgoTypeCenterStop                 // 中心停车
	AlgoTypeIntrusion                  // 入侵检测
)

func (at AlgoType) String() string {
	switch at {
	case AlgoTypeAnalyse:
		return "analyse"
	case AlgoTypeCongestion:
		return "congestion"
	case AlgoTypeRetrograde:
		return "retrograde"
	case AlgoTypeCenterStop:
		return "center_stop"
	case AlgoTypeIntrusion:
		return "intrusion"
	default:
		return "unknown"
	}
}

type Provider interface {
}

type ProviderUtil[P Provider] struct {
	providerMap map[string]*P
}

func NewProviderUtil[P Provider]() ProviderUtil[P] {
	return ProviderUtil[P]{
		providerMap: make(map[string]*P),
	}
}

func (util *ProviderUtil[P]) RegisterProvider(key string, provider *P) {
	util.providerMap[key] = provider
}

func (util *ProviderUtil[P]) DoFunction(t interface{}, f func(fieldValue interface{}, provider P) error, ignoreFieldNil bool) error {
	fieldMap := util.GetFieldMap(t)
	if len(fieldMap) == 0 {
		return nil
	}
	for tag, field := range fieldMap {
		provider, ok := util.GetProvider(tag)
		if !ok {
			continue
		}
		if ignoreFieldNil && field.IsNil() {
			continue
		}
		err := f(field.Interface(), *provider)
		if err != nil {
			return err
		}
	}
	return nil
}

func (util *ProviderUtil[P]) SetField(t interface{}, value func(provider P) interface{}) {
	fieldMap := util.GetFieldMap(t)
	if len(fieldMap) == 0 {
		return
	}
	for tag, field := range fieldMap {
		provider, ok := util.GetProvider(tag)
		if !ok {
			continue
		}
		v := value(*provider)
		if v == nil {
			continue
		}
		field.Set(reflect.ValueOf(v))
	}
}

func (util *ProviderUtil[P]) CopyField(s, t interface{}, value func(fieldValue interface{}, provider P) interface{}) {
	sourceFieldMap, targetFieldMap := util.GetFieldMap(s), util.GetFieldMap(t)
	if len(sourceFieldMap) == 0 || len(targetFieldMap) == 0 {
		return
	}
	for tag, field := range targetFieldMap {
		sf, ok := sourceFieldMap[tag]
		if !ok || sf.IsNil() {
			continue
		}
		provider, ok := util.GetProvider(tag)
		if !ok {
			continue
		}
		v := value(sf.Interface(), *provider)
		if v == nil {
			continue
		}
		field.Set(reflect.ValueOf(v))
	}
}

func (util *ProviderUtil[P]) GetFieldMap(t interface{}) map[string]reflect.Value {
	if t == nil {
		return nil
	}
	typeOf := reflect.TypeOf(t)
	if typeOf.Kind() == reflect.Ptr {
		typeOf = typeOf.Elem()
	}
	numField := typeOf.NumField()
	algoFieldsMap := make(map[int]string)
	for i := 0; i < numField; i++ {
		field := typeOf.Field(i)
		tag := field.Tag.Get(AlgorithmTag)
		if tag != "" {
			algoFieldsMap[i] = tag
		}
	}
	if len(algoFieldsMap) == 0 {
		return nil
	}

	fieldMap := make(map[string]reflect.Value)

	valueOf := reflect.ValueOf(t)
	if valueOf.Kind() == reflect.Ptr {
		valueOf = valueOf.Elem()
	}
	for i, tag := range algoFieldsMap {
		fieldMap[tag] = valueOf.Field(i)
	}
	return fieldMap
}

func (util *ProviderUtil[P]) GetProvider(key string) (*P, bool) {
	provider, ok := util.providerMap[key]
	return provider, ok
}
