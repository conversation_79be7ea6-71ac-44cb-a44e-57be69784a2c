package common

import (
	"time"
)

const (
	PeriodDateFormat = "2006-01-02"
	PeriodTimeFormat = "15:04:05"
)

type Period struct {
	Keys  []int        `json:"keys,omitempty"`
	Range *PeriodRange `json:"range,omitempty"`
	Time  *PeriodTime  `json:"time"`
}

type PeriodRange struct {
	Begin string `json:"begin"`
	End   string `json:"end"`
}

type PeriodTime struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

func (p Period) IsInRange(now time.Time) (bool, error) {
	//
}
