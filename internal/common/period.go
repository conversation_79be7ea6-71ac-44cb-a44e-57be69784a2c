package common

import (
	"time"
)

const (
	PeriodDateFormat = "2006-01-02"
	PeriodTimeFormat = "15:04:05"
)

type Period struct {
	Keys  []int        `json:"keys,omitempty"`
	Range *PeriodRange `json:"range,omitempty"`
	Time  *PeriodTime  `json:"time"`
}

type PeriodRange struct {
	Begin string `json:"begin"`
	End   string `json:"end"`
}

type PeriodTime struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

func (p *Period) IsInPeriod(t time.Time) bool {
	if p.Time == nil {
		return false
	}

	if !p.isTimeInRange(t) {
		return false
	}

	if len(p.Keys) > 0 {
		return p.isWeekdayInKeys(t)
	}

	if p.Range != nil {
		return p.isDateInRange(t)
	}

	return true
}

func (p *Period) isTimeInRange(t time.Time) bool {
	timeStr := t.Format(PeriodTimeFormat)

	if p.Time.Start == p.Time.End {
		return true
	}

	if p.Time.Start <= p.Time.End {
		return timeStr >= p.Time.Start && timeStr <= p.Time.End
	}

	return timeStr >= p.Time.Start || timeStr <= p.Time.End
}

func (p *Period) isWeekdayInKeys(t time.Time) bool {
	weekday := int(t.Weekday())
	if weekday == 0 {
		weekday = 7
	}

	for _, key := range p.Keys {
		if key == weekday {
			return true
		}
	}
	return false
}

func (p *Period) isDateInRange(t time.Time) bool {
	dateStr := t.Format(PeriodDateFormat)
	return dateStr >= p.Range.Begin && dateStr <= p.Range.End
}
