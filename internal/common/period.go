package common

import (
	"time"
)

const (
	PeriodDateFormat = "2006-01-02"
	PeriodTimeFormat = "15:04:05"
)

type Period struct {
	Keys  []int        `json:"keys,omitempty"`
	Range *PeriodRange `json:"range,omitempty"`
	Time  *PeriodTime  `json:"time"`
}

type PeriodRange struct {
	Begin string `json:"begin"`
	End   string `json:"end"`
}

type PeriodTime struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

// IsInPeriod 验证给定时间是否在此 period 范围内
// t: 要验证的时间
// 返回: true 表示在范围内，false 表示不在范围内
func (p *Period) IsInPeriod(t time.Time) bool {
	// 如果 Time 为空，则认为不在范围内
	if p.Time == nil {
		return false
	}

	// 验证时间部分
	if !p.isTimeInRange(t) {
		return false
	}

	// 如果有 Keys，验证星期几
	if len(p.Keys) > 0 {
		return p.isWeekdayInKeys(t)
	}

	// 如果有 Range，验证日期范围
	if p.Range != nil {
		return p.isDateInRange(t)
	}

	// 如果既没有 Keys 也没有 Range，则只验证时间部分（已经验证过了）
	return true
}

// isTimeInRange 验证时间是否在指定的时间范围内
func (p *Period) isTimeInRange(t time.Time) bool {
	timeStr := t.Format(PeriodTimeFormat)

	// 如果开始时间和结束时间相同，表示全天
	if p.Time.Start == p.Time.End {
		return true
	}

	// 正常情况：开始时间 <= 当前时间 <= 结束时间
	if p.Time.Start <= p.Time.End {
		return timeStr >= p.Time.Start && timeStr <= p.Time.End
	}

	// 跨天情况：例如 22:00:00 到 06:00:00
	return timeStr >= p.Time.Start || timeStr <= p.Time.End
}

// isWeekdayInKeys 验证星期几是否在指定的 keys 中
func (p *Period) isWeekdayInKeys(t time.Time) bool {
	// Go 的 Weekday: Sunday=0, Monday=1, ..., Saturday=6
	// 我们的 Keys: Monday=1, Tuesday=2, ..., Sunday=7
	weekday := int(t.Weekday())
	if weekday == 0 {
		weekday = 7 // Sunday
	}

	for _, key := range p.Keys {
		if key == weekday {
			return true
		}
	}
	return false
}

// isDateInRange 验证日期是否在指定的日期范围内
func (p *Period) isDateInRange(t time.Time) bool {
	dateStr := t.Format(PeriodDateFormat)
	return dateStr >= p.Range.Begin && dateStr <= p.Range.End
}
