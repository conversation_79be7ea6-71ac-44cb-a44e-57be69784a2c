package warning

type Type int

const (
	NotifyTypeCongestionStart Type = iota // 拥堵开始
	NotifyTypeCongestionEnd               // 拥堵结束
	NotifyTypeReverseStart                // 逆行开始
	NotifyTypeReverseEnd                  // 逆行结束
	NotifyTypeStallStart                  // 中停开始
	NotifyTypeStallEnd                    // 中停结束
	NotifyTypeIntrusionStart              // 入侵开始
	NotifyTypeIntrusionEnd                // 入侵结束
	NotifyTypeStatData                    // 轨迹统计数据
)

const (
	TopicEventNotify = "msg_event_notify"
)

type NotifyData struct {
	Type    Type  `json:"type"`
	Data    any   `json:"data"` // 数据类型根据Type不同而不同
	EvtTime int64 `json:"evt_time"`
}
type StallData struct {
	CID     string `json:"cid"`      // 相机ID
	RID     string `json:"rid"`      // 区域ID
	TID     string `json:"tid"`      // 轨迹ID
	Img     string `json:"img"`      // 图片URL
	ObjType string `json:"obj_type"` // 对象类型
}
type CongestionData struct {
	CID     string `json:"cid"`      // 相机ID
	RID     string `json:"rid"`      // 区域ID
	TID     string `json:"tid"`      // 轨迹ID
	Img     string `json:"img"`      // 图片URL
	ObjType string `json:"obj_type"` // 对象类型
}

type ReverseData struct {
	CID     string `json:"cid"`      // 相机ID
	RID     string `json:"rid"`      // 区域ID
	TID     string `json:"tid"`      // 轨迹ID
	Img     string `json:"img"`      // 图片URL
	ObjType string `json:"obj_type"` // 对象类型
}
type IntrusionData struct {
	CID     string `json:"cid"`      // 相机ID
	RID     string `json:"rid"`      // 区域ID
	TID     string `json:"tid"`      // 轨迹ID
	Img     string `json:"img"`      // 图片URL
	ObjType string `json:"obj_type"` // 对象类型
}

// TrackStatData 轨迹统计数据
type TrackStatData struct {
	CID       string `json:"cid"`        // 相机ID
	RID       string `json:"rid"`        // 区域ID
	StartTime int64  `json:"start_time"` // 开始时间
	EndTime   int64  `json:"end_time"`   // 结束时间
	Stage1    int64  `json:"stage1"`     // 车辆通行时间小于5秒时间
	Stage2    int64  `json:"stage2"`     // 车辆通行时间小于10秒时间
	Stage3    int64  `json:"stage3"`     // 车辆通行时间小于30秒时间
	Stage4    int64  `json:"stage4"`     // 车辆通行时间小于60秒时间
	Stage5    int64  `json:"stage5"`     // 车辆通行时间小于120秒时间
	Stage6    int64  `json:"stage6"`     // 车辆通行时间小于300秒时间
	Stage7    int64  `json:"stage7"`     // 车辆通行时间小于600秒时间
	Stage8    int64  `json:"stage8"`     // 车辆通行时间小于1200秒时间
	Stage9    int64  `json:"stage9"`     // 车辆通行时间小于2400秒时间
	Stage10   int64  `json:"stage10"`    // 车辆通行时间大于2400秒时间
	VehTime   int64  `json:"veh_time"`   // 车辆通行总时间
	VehCount  int    `json:"veh_count"`  // 通行车辆总数
}
