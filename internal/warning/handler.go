package warning

import (
	"encoding/json"
	"stvpp/internal/modules"

	"github.com/iWay7/go-common/logger"
	"github.com/nats-io/nats.go"
)

// subscribeMsgEventNotify 订阅事件通知
func subscribeMsgEventNotify() {
	_, err := nc.Subscribe(TopicEventNotify, func(msg *nats.Msg) {
		var notify NotifyData
		err := json.Unmarshal(msg.Data, &notify)
		if err != nil {
			logger.Ef("failed to unmarshal notify data, %v", err)
			return
		}

		switch notify.Type {
		case NotifyTypeCongestionStart, NotifyTypeCongestionEnd:
			handleCongestionData(&notify)
		case NotifyTypeReverseStart, NotifyTypeReverseEnd:
			handleReverseData(&notify)
		case NotifyTypeStallStart, NotifyTypeStallEnd:
			handleStallData(&notify)
		case NotifyTypeIntrusionStart, NotifyTypeIntrusionEnd:
			handleIntrusionData(&notify)
		case NotifyTypeStatData:
			handleTrackStatData(&notify)
		default:
			logger.Wf("unhandled notify type: %d", notify.Type)
		}
	})
	if err != nil {
		logger.Ef("订阅预警通知失败: %s, %v", TopicEventNotify, err)
	}
}

// handleCongestionData 处理拥堵数据
func handleCongestionData(notify *NotifyData) {
	var data CongestionData
	if err := unmarshalData(notify.Data, &data); err != nil {
		logger.Ef("failed to unmarshal congestion data, %v", err)
		return
	}
	logger.If("received congestion data (type %d): %+v", notify.Type, data)
}

// handleReverseData 处理逆行数据
func handleReverseData(notify *NotifyData) {
	var data ReverseData
	if err := unmarshalData(notify.Data, &data); err != nil {
		logger.Ef("failed to unmarshal reverse data, %v", err)
		return
	}
	logger.If("received reverse data (type %d): %+v", notify.Type, data)
}

// handleStallData 处理中停数据
func handleStallData(notify *NotifyData) {
	var data StallData
	if err := unmarshalData(notify.Data, &data); err != nil {
		logger.Ef("failed to unmarshal stall data, %v", err)
		return
	}
	logger.If("received stall data (type %d): %+v", notify.Type, data)
}

// handleIntrusionData 处理入侵数据
func handleIntrusionData(notify *NotifyData) {
	var data IntrusionData
	if err := unmarshalData(notify.Data, &data); err != nil {
		logger.Ef("failed to unmarshal intrusion data, %v", err)
		return
	}
	logger.If("received intrusion data (type %d): %+v", notify.Type, data)
}

// handleTrackStatData 处理轨迹统计数据
func handleTrackStatData(notify *NotifyData) {
	var data TrackStatData
	if err := unmarshalData(notify.Data, &data); err != nil {
		logger.Ef("failed to unmarshal track stat data, %v", err)
		return
	}

	dao := modules.GetVehPassStatDao()
	stat := &modules.VehPassStat{
		CID:       data.CID,
		RID:       data.RID,
		StartTime: data.StartTime,
		EndTime:   data.EndTime,
		Stage1:    data.Stage1,
		Stage2:    data.Stage2,
		Stage3:    data.Stage3,
		Stage4:    data.Stage4,
		Stage5:    data.Stage5,
		Stage6:    data.Stage6,
		Stage7:    data.Stage7,
		Stage8:    data.Stage8,
		Stage9:    data.Stage9,
		Stage10:   data.Stage10,
		VehTime:   data.VehTime,
		VehCount:  data.VehCount,
	}
	if err := dao.Add(stat); err != nil {
		logger.Ef("failed to create track stat data, %v", err)
	}
}

func unmarshalData(data any, v any) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return json.Unmarshal(jsonData, v)
}
