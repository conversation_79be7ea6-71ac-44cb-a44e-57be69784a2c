package mq

import (
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/nats-io/nats.go"
)

func TestRequest_Reply(t *testing.T) {
	nc1 := connect("nc1")

	subscribe(nc1, "hello")

	send := connect("send")

	for i := 0; i < 9; i++ {
		// go func() {
		resp, err := send.Request("hello", []byte(strconv.Itoa(i)), time.Second*10)
		// err := send.Publish("hello", []byte(strconv.Itoa(i)))
		// reply := send.NewInbox()
		// err := send.PublishRequest("hello", reply, []byte(strconv.Itoa(i)))
		if err != nil {
			panic(err)
		}
		// send.Subscribe(reply, func(msg *nats.Msg) {
		// 	go func(msg *nats.Msg) {
		// 		t.Log(string(msg.Data))
		// 	}(msg)
		// })
		t.Log(string(resp.Data))
		// }()
	}
	select {}
}

func connect(name string) *nats.Conn {
	nc, err := nats.Connect("nats://192.168.9.27:4222", nats.Name(name))
	if err != nil {
		panic(err)
	}
	return nc
}

func subscribe(c *nats.Conn, subject string) error {
	_, err := c.Subscribe(subject, func(msg *nats.Msg) {
		go func(msg *nats.Msg) {
			ss := fmt.Sprintf("time: %v\t%s\t%s", time.Now().UnixMilli(), c.Opts.Name, string(msg.Data))
			// fmt.Println(ss)
			i, _ := strconv.Atoi(string(msg.Data))
			time.Sleep(time.Second * 3)
			if i%3 == 0 || true {
				msg.Respond([]byte(ss))
			}
		}(msg)
	})
	return err
}
