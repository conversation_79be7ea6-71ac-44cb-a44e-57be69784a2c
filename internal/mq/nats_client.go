package mq

import (
	"encoding/json"
	"fmt"
	"stvpp/internal/common"
	"time"

	"github.com/nats-io/nats.go"
)

// NatsMQClient is the NATS implementation of MQClient
type NatsMQClient struct {
	nc *nats.Conn
}

// NewNatsMQClient creates a new NATS MQ client
func NewNatsMQClient(nc *nats.Conn) *NatsMQClient {
	return &NatsMQClient{
		nc: nc,
	}
}

func (m *NatsMQClient) Subscribe(subject string, handler func(msg *common.Msg)) error {
	_, err := m.nc.Subscribe(subject, func(msg *nats.Msg) {
		go func(msg *nats.Msg) {
			handler(&common.Msg{
				Subj:  msg.Subject,
				Data:  msg.Data,
				Reply: msg.Reply,
			})
		}(msg)
	})
	if err != nil {
		return fmt.Errorf("nats.Subscribe(%s, handler): %v", subject, err)
	}
	return nil
}

func (m *NatsMQClient) Request(subject string, data interface{}, timeout time.Duration) (*common.Msg, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("json.Marshal(data): %v", err)
	}

	msg, err := m.nc.Request(subject, jsonData, timeout)
	if err != nil {
		return nil, fmt.Errorf("nats request for subject %s failed: %v", subject, err)
	}

	return &common.Msg{
		Subj: subject,
		Data: msg.Data,
	}, nil
}

func (m *NatsMQClient) Publish(subject string, data interface{}) (string, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("json.Marshal(data): %v", err)
	}
	reply := m.nc.NewInbox()
	err = m.nc.PublishRequest(subject, reply, jsonData)

	if err != nil {
		return "", fmt.Errorf("nats.Publish(%s, data): %v", subject, err)
	}

	return reply, nil
}
