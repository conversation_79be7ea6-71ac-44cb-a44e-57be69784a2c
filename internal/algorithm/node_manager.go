package algorithm

import (
	"encoding/json"
	"fmt"
	"stvpp/internal/common"
	"sync"
	"time"

	"github.com/iWay7/go-common/misc"
)

const (
	nodeTimeout     = 60 * time.Second // 节点超时时间
	cleanupInterval = 10 * time.Second // 清理间隔

	// EventNodeInit 节点初始化事件主题
	EventNodeInit = "node_init"
	// EventNodeAdded 节点添加事件主题
	EventNodeAdded = "node_added"
	// EventNodeOffline 节点离线事件主题
	EventNodeOffline = "node_offline"
)

type NodeInfo struct {
	MsgHeartbeat
	NodeID   string
	lastSeen time.Time
}

// NodeManager 节点管理器
type NodeManager struct {
	mu                sync.RWMutex
	nodes             map[string]*NodeInfo
	nodeIDs           []string // 用于轮询的有序节点ID列表
	lastUsedNodeIndex int
	mqClient          common.MQClient
}

// NewNodeManager 创建节点管理器
func NewNodeManager(mqClient common.MQClient) *NodeManager {
	return &NodeManager{
		nodes:             make(map[string]*NodeInfo),
		nodeIDs:           make([]string, 0),
		lastUsedNodeIndex: -1, // 从-1开始，第一次选择时会是0
		mqClient:          mqClient,
	}
}

// GetAliveNodes 获取所有存活的节点信息
func (nm *NodeManager) GetAliveNodes() []NodeInfo {
	nm.mu.RLock()
	defer nm.mu.RUnlock()

	var aliveNodes []NodeInfo
	for _, node := range nm.nodes {
		aliveNodes = append(aliveNodes, *node)
	}
	return aliveNodes
}

// IsNodeAlive 检查节点是否存活
func (nm *NodeManager) IsNodeAlive(nodeID string) bool {
	nm.mu.RLock()
	defer nm.mu.RUnlock()
	_, exists := nm.nodes[nodeID]
	return exists
}

// SelectNextNode 使用轮询算法选择下一个可用节点
func (nm *NodeManager) SelectNextNode() (*NodeInfo, error) {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	if len(nm.nodeIDs) == 0 {
		return nil, fmt.Errorf("no alive nodes available")
	}

	// 轮询索引
	nm.lastUsedNodeIndex = (nm.lastUsedNodeIndex + 1) % len(nm.nodeIDs)
	selectedNodeID := nm.nodeIDs[nm.lastUsedNodeIndex]
	selectedNodeInfo := nm.nodes[selectedNodeID]

	return selectedNodeInfo, nil
}

// Start 启动节点管理器
func (nm *NodeManager) Start() error {
	if err := nm.mqClient.Subscribe(TopicHeartbeat, nm.handleHeartbeat); err != nil {
		return err
	}
	misc.ScheduleTask(misc.Now().Add(time.Second*30).Unix(), func() int64 {
		nm.cleanupDeadNodes()
		return misc.Now().Add(cleanupInterval).Unix()
	})
	return nil
}

// cleanupDeadNodes 清理死节点
func (nm *NodeManager) cleanupDeadNodes() {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	now := time.Now()
	for i := len(nm.nodeIDs) - 1; i >= 0; i-- {
		nodeID := nm.nodeIDs[i]
		nodeInfo, exists := nm.nodes[nodeID]

		if !exists || now.Sub(nodeInfo.lastSeen) > nodeTimeout {
			trackerLogger.Appendf("W: 节点管理器: 节点 '%s' 超时并被移除", nodeID)
			delete(nm.nodes, nodeID)
			nm.nodeIDs = append(nm.nodeIDs[:i], nm.nodeIDs[i+1:]...)

			// 如果移除的节点在当前轮询索引之前或就是当前索引，需要调整索引
			if i <= nm.lastUsedNodeIndex {
				nm.lastUsedNodeIndex--
			}

			// 使用全局事件总线发布节点离线事件
			misc.BroadcastEvent(EventNodeOffline, nodeID)
			trackerLogger.Appendf("I: 节点管理器: 已发布节点 '%s' 的离线事件", nodeID)
		}
	}
}

// handleHeartbeat 处理节点心跳消息
func (nm *NodeManager) handleHeartbeat(msg *common.Msg) {
	var heartbeat MsgHeartbeat
	if err := json.Unmarshal(msg.Data, &heartbeat); err != nil {
		trackerLogger.Appendf("W: 节点管理器: 无法解析心跳消息: %v", err)
		return
	}

	nm.mu.Lock()
	nodeID := heartbeat.SIP // 使用 SIP 作为 NodeID

	oldLen := len(nm.nodes)
	// 更新节点信息
	nodeInfo, exists := nm.nodes[nodeID]
	if !exists {
		nodeInfo = &NodeInfo{}
	}
	nodeInfo.NodeID = nodeID
	nodeInfo.MsgHeartbeat = heartbeat
	nodeInfo.lastSeen = time.Now()
	nm.nodes[nodeID] = nodeInfo
	nm.mu.Unlock()

	if oldLen == 0 {
		trackerLogger.Appendf("I: 节点管理器: 节点初始化: %s", nodeID)
		nm.nodeIDs = append(nm.nodeIDs, nodeID)
		misc.BroadcastEvent(EventNodeInit, nodeID)
	} else if !exists {
		trackerLogger.Appendf("I: 节点管理器: 发现新节点: %s", nodeID)
		nm.nodeIDs = append(nm.nodeIDs, nodeID)
		misc.BroadcastEvent(EventNodeAdded, nodeID)
	}
}
