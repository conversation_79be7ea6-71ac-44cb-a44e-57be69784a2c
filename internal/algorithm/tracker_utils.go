package algorithm

import (
	"encoding/json"
	"fmt"
)

// TrackerStatus 追踪器状态枚举
type TrackerStatus int

const (
	StatusIdle       TrackerStatus = iota // 空闲状态
	StatusConfigured                      // 已配置
	StatusRunning                         // 运行中
	StatusPaused                          // 已暂停
	StatusStopped                         // 已停止
	StatusError                           // 错误状态
)

var (
	trackerActionMap = map[string]*TrackerAction{
		"snap":   {Action: "snap", Function: (*VideoTracker).Snap},
		"start":  {Action: "start", Function: (*VideoTracker).Start},
		"stop":   {Action: "stop", Function: (*VideoTracker).Stop},
		"pause":  {Action: "pause", Function: (*VideoTracker).Pause},
		"resume": {Action: "resume", Function: (*VideoTracker).Resume},
		"focus":  {Action: "focus", Function: (*VideoTracker).Focus},
		"delete": {Action: "delete", Function: (*VideoTracker).Delete},
		"restart": {Action: "restart", Function: func(vt *VideoTracker) error {
			if err := vt.Add(); err != nil {
				return err
			}
			if err := vt.Configure(MsgTrackCfg{Zones: vt.Zones, ImageSize: vt.ImageSize}); err != nil {
				return err
			}
			if err := vt.Start(); err != nil {
				return err
			}
			return nil
		}},
	}
)

func (t TrackerStatus) Int() int {
	return int(t)
}

func (t TrackerStatus) String() string {
	switch t {
	case StatusIdle:
		return "空闲"
	case StatusConfigured:
		return "已配置"
	case StatusRunning:
		return "运行中"
	case StatusPaused:
		return "已暂停"
	case StatusStopped:
		return "已停止"
	case StatusError:
		return "错误"
	default:
		return "未知"
	}
}

type TrackerAction struct {
	Action   string                    `json:"action"`
	Function func(*VideoTracker) error `json:"-"`
}

func (t *TrackerAction) UnmarshalJSON(bytes []byte) error {
	var action string
	if err := json.Unmarshal(bytes, &action); err != nil {
		return err
	}
	actionFunc, exists := trackerActionMap[action]
	if !exists {
		return fmt.Errorf("invalid action: %s", action)
	}
	t.Action = action
	t.Function = actionFunc.Function
	return nil
}

// ValidateTrackZone 验证追踪区域
func ValidateTrackZone(zone TrackZone) error {
	// 验证区域ID
	if zone.ZID == "" {
		return fmt.Errorf("zone id cannot be empty")
	}

	// 验证顶点坐标
	if len(zone.XYXY) < 6 { // 至少需要3个点（6个坐标值）
		return fmt.Errorf("zone must have at least 3 points")
	}

	// 验证方向参数
	if zone.Direction < -1 || zone.Direction > 1 {
		return fmt.Errorf("invalid direction value: %d", zone.Direction)
	}

	return nil
}

// PrintTrackerStatus 打印追踪器状态计数
func PrintTrackerStatus(tm *TrackerManager) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if len(tm.trackers) == 0 {
		return
	}

	status := make(map[string]int)
	status["total"] = len(tm.trackers)
	status["idle"] = 0
	status["configured"] = 0
	status["running"] = 0
	status["paused"] = 0
	status["stopped"] = 0
	status["error"] = 0

	for _, tracker := range tm.trackers {
		tracker.mu.RLock()
		switch tracker.Status {
		case StatusIdle:
			status["idle"]++
		case StatusConfigured:
			status["configured"]++
		case StatusRunning:
			status["running"]++
		case StatusPaused:
			status["paused"]++
		case StatusStopped:
			status["stopped"]++
		case StatusError:
			status["error"]++
		}
		tracker.mu.RUnlock()
	}

	trackerLogger.Appendf("D: 追踪器状态: 总数=%d, 空闲=%d, 已配置=%d, 运行中=%d, 已暂停=%d, 已停止=%d, 错误=%d",
		status["total"], status["idle"], status["configured"], status["running"], status["paused"], status["stopped"], status["error"])
}
