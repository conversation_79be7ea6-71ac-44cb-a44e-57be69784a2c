package algorithm

import (
	"stvpp/global"
	"stvpp/internal/mq"
	"sync"

	"github.com/iWay7/go-common/logger"
	"github.com/nats-io/nats.go"
)

var (
	trackerManager *TrackerManager
	nodeManager    *NodeManager
	once           sync.Once
	trackerLogger  *logger.MiniLogger
)

func Init(nc *nats.Conn) {
	once.Do(func() {
		trackerLogger = logger.NewFileLogger(global.TrackerLogFile, 256*1024*1024)
		mqClient := mq.NewNatsMQClient(nc)

		// 1. 初始化NodeManager
		nodeManager = NewNodeManager(mqClient)
		if err := nodeManager.Start(); err != nil {
			trackerLogger.Appendf("E: 启动节点管理器失败: %v", err)
		}

		// 2. 初始化TrackerManager
		trackerManager = NewTrackerManager(mqClient)
		if err := trackerManager.Start(); err != nil {
			trackerLogger.Appendf("E: 启动追踪管理器失败: %v", err)
		}
	})
}

// GetTrackerManager 获取追踪管理器实例
func GetTrackerManager() *TrackerManager {
	return trackerManager
}

// GetNodeManager 获取节点管理器实例
func GetNodeManager() *NodeManager {
	return nodeManager
}
