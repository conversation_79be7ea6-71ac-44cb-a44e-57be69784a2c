package algorithm

import (
	"encoding/json"
	"fmt"
	"stvpp/internal/common"
	"sync"
	"time"
)

const (
	// requestTimeout 定义了NATS请求的默认超时时间
	requestTimeout = 30 * time.Second
)

// VideoTracker 单个视频追踪器
type VideoTracker struct {
	CID           string        // 视频唯一ID
	NodeID        string        // 运行此追踪器的节点ID
	LocalFilePath string        // 本地文件路径
	PlatIp        string        // 平台IP
	PlatPort      string        // 平台端口
	Zones         []TrackZone   // 标注区域
	ImageSize     []int         // 图像宽高[width,height]
	Status        TrackerStatus // 当前状态
	ImgUrl        string        // 图片URL
	mu            sync.RWMutex  // 保护tracker内部状态
	snapMu        sync.Mutex    // 保护截图操作
	snapTime      time.Time     // 截图时间

	// Dependencies
	mqClient     common.MQClient
	onDelete     func(cid string) // Callback to notify manager for deletion
	onStatUpdate func(vt *VideoTracker)
}

// Add 添加视频追踪。
func (vt *VideoTracker) Add() error {
	msg := MsgTrackAdd{
		CID:           vt.CID,
		SIP:           common.GetLocalIP(),
		RIP:           vt.NodeID,
		LocalFilePath: vt.LocalFilePath,
		PlatIp:        vt.PlatIp,
		PlatPort:      vt.PlatPort,
	}
	return vt.atomicOperation(TopicTrackAdd, msg, func(resp interface{}) {
		vt.Status = StatusIdle
		// 获取一张图片
		go func() {
			err := vt.Snap()
			if err != nil {
				trackerLogger.Appendf("E: 视频追踪器[%s]: 截图失败: %v", vt.CID, err)
			}
		}()
	})
}

// Configure 调度配置操作。
func (vt *VideoTracker) Configure(msg MsgTrackCfg) error {
	vt.mu.RLock()
	// 允许在空闲或已配置状态下进行配置
	if vt.Status != StatusIdle && vt.Status != StatusConfigured {
		vt.mu.RUnlock()
		return fmt.Errorf("operation Configure not allowed in current state '%s'", vt.Status.String())
	}

	zones := msg.Zones
	for _, zone := range zones {
		if err := ValidateTrackZone(zone); err != nil {
			vt.mu.RUnlock()
			return err
		}
	}

	vt.mu.RUnlock()

	msg.CID = vt.CID
	msg.SIP = common.GetLocalIP()
	msg.RIP = vt.NodeID
	return vt.atomicOperation(TopicTrackCfg, msg, func(resp interface{}) {
		vt.Zones = msg.Zones
		vt.ImageSize = msg.ImageSize
		vt.Status = StatusConfigured
		trackerLogger.Appendf("I: 视频追踪器[%s]: 状态变更为 -> [%s], 区域数量: %d", vt.CID, vt.Status.String(), len(vt.Zones))
	})
}

// Start 调度启动操作。
func (vt *VideoTracker) Start() error {
	vt.mu.RLock()
	// 允许在已配置或已停止状态下启动
	if vt.Status != StatusConfigured && vt.Status != StatusStopped {
		vt.mu.RUnlock()
		return fmt.Errorf("operation Start not allowed in current state '%s'", vt.Status.String())
	}
	vt.mu.RUnlock()

	msg := MsgTrackStart{
		CID: vt.CID,
		SIP: common.GetLocalIP(),
		RIP: vt.NodeID,
	}
	return vt.atomicOperation(TopicTrackStart, msg, func(resp interface{}) {
		vt.Status = StatusRunning
		trackerLogger.Appendf("I: 视频追踪器[%s]: 状态变更为 -> [%s]", vt.CID, vt.Status.String())
	})
}

// Snap 调度截图操作
func (vt *VideoTracker) Snap() error {
	vt.mu.RLock()
	// 只要不是错误状态，都应该允许截图
	if vt.Status == StatusError {
		vt.mu.RUnlock()
		return fmt.Errorf("operation Snap not allowed in current state '%s'", vt.Status.String())
	}
	vt.mu.RUnlock()

	msg := MsgTrackSnap{
		CID: vt.CID,
		SIP: common.GetLocalIP(),
		RIP: vt.NodeID,
	}
	return vt.atomicOperation(TopicTrackSnap, msg, func(resp interface{}) {
		if imgUrl := resp.(string); imgUrl != "" {
			vt.ImgUrl = imgUrl
			vt.snapTime = time.Now()
		}
	})
}

// Focus 调度聚焦操作
func (vt *VideoTracker) Focus() error {
	vt.mu.RLock()
	if vt.Status != StatusRunning {
		vt.mu.RUnlock()
		return fmt.Errorf("operation Focus not allowed in current state '%s'", vt.Status.String())
	}
	vt.mu.RUnlock()

	msg := MsgTrackFocus{
		CID: vt.CID,
		SIP: common.GetLocalIP(),
		RIP: vt.NodeID,
	}
	return vt.atomicOperation(TopicTrackFocus, msg, nil)
}

// Pause 调度暂停操作
func (vt *VideoTracker) Pause() error {
	vt.mu.RLock()
	if vt.Status != StatusRunning {
		vt.mu.RUnlock()
		return fmt.Errorf("operation Pause not allowed in current state '%s'", vt.Status.String())
	}
	vt.mu.RUnlock()

	msg := MsgTrackPause{
		CID: vt.CID,
		SIP: common.GetLocalIP(),
		RIP: vt.NodeID,
	}
	return vt.atomicOperation(TopicTrackPause, msg, func(resp interface{}) {
		vt.Status = StatusPaused
		trackerLogger.Appendf("I: 视频追踪器[%s]: 状态变更为 -> [%s], 标记为暂停", vt.CID, vt.Status.String())
	})
}

// Resume 调度恢复操作
func (vt *VideoTracker) Resume() error {
	vt.mu.RLock()
	if vt.Status != StatusStopped && vt.Status != StatusPaused {
		vt.mu.RUnlock()
		return fmt.Errorf("operation Resume not allowed in current state '%s'", vt.Status.String())
	}
	vt.mu.RUnlock()

	msg := MsgTrackResume{
		CID: vt.CID,
		SIP: common.GetLocalIP(),
		RIP: vt.NodeID,
	}
	return vt.atomicOperation(TopicTrackResume, msg, func(resp interface{}) {
		vt.Status = StatusRunning
		trackerLogger.Appendf("I: 视频追踪器[%s]: 状态变更为 -> [%s]", vt.CID, vt.Status.String())
	})
}

// Stop 调度停止操作。
func (vt *VideoTracker) Stop() error {
	vt.mu.RLock()
	if vt.Status != StatusRunning {
		vt.mu.RUnlock()
		return fmt.Errorf("operation Stop not allowed in current state '%s'", vt.Status.String())
	}
	vt.mu.RUnlock()

	msg := MsgTrackStop{
		CID: vt.CID,
		SIP: common.GetLocalIP(),
		RIP: vt.NodeID,
	}
	return vt.atomicOperation(TopicTrackStop, msg, func(resp interface{}) {
		vt.Status = StatusStopped
		trackerLogger.Appendf("I: 视频追踪器[%s]: 状态变更为 -> [%s]", vt.CID, vt.Status.String())
	})
}

// Delete 调度删除操作。
func (vt *VideoTracker) Delete() error {
	msg := MsgTrackDel{
		CID: vt.CID,
		SIP: common.GetLocalIP(),
		RIP: vt.NodeID,
	}
	err := vt.atomicOperation(TopicTrackDel, msg, nil)
	if err != nil {
		return err
	}
	// 成功后，在锁外触发删除回调
	if vt.onDelete != nil {
		go vt.onDelete(vt.CID)
	}
	return nil
}

// GetStatus 返回追踪器的当前状态。
func (vt *VideoTracker) GetStatus() TrackerStatus {
	vt.mu.RLock()
	defer vt.mu.RUnlock()
	return vt.Status
}

// GetImgUrl 返回追踪器的当前图片URL。
func (vt *VideoTracker) GetImgUrl(imgTimeout time.Duration) (string, error) {
	vt.snapMu.Lock()
	defer vt.snapMu.Unlock()
	newImg := false
	if time.Now().Sub(vt.snapTime) > imgTimeout {
		newImg = true
	}
	if vt.ImgUrl == "" || newImg {
		if err := vt.Snap(); err != nil {
			return "", err

		}
		if vt.ImgUrl == "" {
			return "", fmt.Errorf("img url not available even after snap for tracker: %s", vt.CID)
		}
	}
	return vt.ImgUrl, nil
}

func (vt *VideoTracker) GetZone(zid string) (TrackZone, error) {
	vt.mu.RLock()
	defer vt.mu.RUnlock()
	for _, zone := range vt.Zones {
		if zone.ZID == zid {
			return zone, nil
		}
	}
	return TrackZone{}, fmt.Errorf("zone not found: %s", zid)
}

// atomicOperation 执行一个原子操作：它在网络IO期间不加锁，在处理响应和状态更新时加锁。
// onSuccess 回调函数会在操作成功且持有锁的情况下被调用。
func (vt *VideoTracker) atomicOperation(topic string, msg interface{}, onSuccess func(resp interface{})) error {
	trackerLogger.Appendf("I: 视频追踪器[%s]: 正在向节点 '%s' 执行 '%s' 操作。", vt.CID, vt.NodeID, topic)

	if topic != TopicTrackSnap {
		vt.mu.Lock()
		defer vt.mu.Unlock()
	}

	resp, err := requestTrackResp(vt.mqClient, topic, msg, requestTimeout, true)

	if err != nil {
		vt.Status = StatusError
		wrappedErr := fmt.Errorf("'%s' operation failed for tracker '%s': %v", topic, vt.CID, err)
		trackerLogger.Appendf("E: %v", wrappedErr)
		return wrappedErr
	}

	if onSuccess != nil {
		onSuccess(resp.Data)
	}
	trackerLogger.Appendf("I: 视频追踪器[%s]: 操作 '%s' 成功完成。", vt.CID, topic)

	if vt.onStatUpdate != nil {
		go vt.onStatUpdate(vt)
	}

	return nil
}

// requestTrackResp 是一个内部辅助函数，用于发送请求并专门将响应解码为 MsgTrackResp
func requestTrackResp(mqClient common.MQClient, opType string, msg interface{}, timeout time.Duration, checkSuccess bool) (*MsgTrackResp, error) {
	rawData, err := mqClient.Request(opType, msg, timeout)
	if err != nil {
		return nil, err // 直接传递底层错误
	}
	var resp *MsgTrackResp
	if err = json.Unmarshal(rawData.Data, &resp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: '%s' -> %v", string(rawData.Data), err)
	}
	if checkSuccess {
		if err = resp.CheckSuccess(); err != nil {
			return nil, err
		}
	}
	return resp, nil
}
