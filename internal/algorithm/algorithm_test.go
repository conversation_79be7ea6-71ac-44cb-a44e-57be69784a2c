package algorithm

import (
	"context"
	"encoding/json"
	"fmt"
	"stvpp/internal/common"
	"stvpp/internal/mq"
	"testing"
	"time"

	"github.com/iWay7/go-common/logger"
	"github.com/nats-io/nats.go"
)

// AlgorithmClient 模拟一个算法工作节点客户端
type AlgorithmClient struct {
	nodeID   string
	mqClient common.MQClient
	ctx      context.Context
	cancel   context.CancelFunc
}

// NewAlgorithmClient 创建一个新的算法客户端实例
func NewAlgorithmClient(mqClient common.MQClient) *AlgorithmClient {
	ctx, cancel := context.WithCancel(context.Background())
	// 使用更简洁的ID
	nodeID := fmt.Sprintf("192.168.1.%d", time.Now().UnixMilli()%255)
	logger.If("正在创建模拟算法客户端, ID: %s", nodeID)

	return &AlgorithmClient{
		nodeID:   nodeID,
		mqClient: mqClient,
		ctx:      ctx,
		cancel:   cancel,
	}
}

// Start 启动客户端，开始订阅消息并发送心跳
func (c *AlgorithmClient) Start() error {
	if err := c.subscribeToTopics(); err != nil {
		return fmt.Errorf("client '%s' failed to subscribe to messages: %v", c.nodeID, err)
	}
	go c.startHeartbeat()
	logger.If("客户端 '%s' 已启动", c.nodeID)
	return nil
}

// subscribeToTopics 订阅所有相关的任务主题
func (c *AlgorithmClient) subscribeToTopics() error {
	subscriptions := []string{
		TopicTrackAdd,
		TopicTrackDel,
		TopicTrackSnap,
		TopicTrackCfg,
		TopicTrackStart,
		TopicTrackStop,
		TopicTrackFocus,
		TopicTrackPause,
		TopicTrackResume,
		TopicTrackReset,
	}

	for _, subject := range subscriptions {
		if err := c.mqClient.Subscribe(subject, c.handleRequest(subject)); err != nil {
			return err
		}
	}
	return nil
}

// handleRequest 创建一个通用的请求处理器
func (c *AlgorithmClient) handleRequest(operation string) func(msg *common.Msg) {
	return func(msg *common.Msg) {
		var req map[string]interface{}
		if err := json.Unmarshal(msg.Data, &req); err != nil {
			logger.Ef("[%s] 无法解析 %s 消息: %v", c.nodeID, operation, err)
			return
		}

		cid, _ := req["cid"].(string)
		rip, _ := req["rip"].(string)

		// 检查消息是否是发给本节点的
		if rip != "" && rip != c.nodeID {
			// logger.If("[%s] 收到 %s 命令, 但不是发给我的, 忽略。 rip: %s, nodeID: %s", c.nodeID, operation, rip, c.nodeID)
			return
		}

		logger.If("[%s] 收到 %s 命令, 追踪器 ID: %s, data: %v", c.nodeID, operation, cid, string(msg.Data))

		// 构造响应
		response := MsgTrackResp{
			Code: CodeSuccess,
			Msg:  "OK",
			Data: "",
		}

		// 模拟处理，比如ADD/CFG/START/SNAP后返回一个图片URL
		if operation == TopicTrackSnap {
			time.Sleep(time.Second * 3)
			response.Data = fmt.Sprintf("http://%s/images/%s-%v.jpg", c.nodeID, cid, time.Now().UnixMilli())
		}

		if operation == TopicTrackStop {
			time.Sleep(time.Second * 10)
		}

		// 发送响应
		if _, err := c.mqClient.Publish(msg.Reply, response); err != nil {
			logger.Ef("[%s] 发送响应失败: %v", c.nodeID, err)
		}
		logger.If("[%s] 发送 [%s] 响应成功: %v", c.nodeID, operation, cid)
	}
}

// startHeartbeat 启动一个goroutine，定期发送心跳消息
func (c *AlgorithmClient) startHeartbeat() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.publishAliveMessage()
		case <-c.ctx.Done():
			logger.If("[%s] 心跳已停止", c.nodeID)
			return
		}
	}
}

// publishAliveMessage 发布一条节点存活消息
func (c *AlgorithmClient) publishAliveMessage() {
	msg := MsgHeartbeat{
		SIP:       c.nodeID,
		Service:   "SF", // SF for 算法服务
		StartTime: time.Now().UnixMilli(),
		SysTime:   time.Now().UnixMilli(),
	}

	if _, err := c.mqClient.Publish(TopicHeartbeat, msg); err != nil {
		logger.Ef("[%s] 发送心跳失败: %v", c.nodeID, err)
	}
}

func TestAlgorithm(t *testing.T) {
	// 替换为你的NATS服务器地址
	connect, err := nats.Connect("nats://************:4222")
	if err != nil {
		t.Fatalf("无法连接到NATS: %v", err)
	}
	defer connect.Close()

	// 创建并启动两个模拟的算法客户端
	client1 := NewAlgorithmClient(mq.NewNatsMQClient(connect))
	client1.Start()

	// client2 := NewAlgorithmClient(NewNatsMQClient(connect))
	// client2.Start()

	// 保持测试运行
	select {}
}

func TestSubscribe(t *testing.T) {
	connect, err := nats.Connect("nats://************:4222")
	if err != nil {
		t.Fatalf("无法连接到NATS: %v", err)
	}
	defer connect.Close()

	connect.Subscribe(TopicTrackAdd, func(msg *nats.Msg) {
		logger.If("[Monitor] 收到消息 主题: %s, 回复: %s, 数据: %s", msg.Subject, msg.Reply, string(msg.Data))
	})
	connect.Subscribe(TopicTrackCfg, func(msg *nats.Msg) {
		logger.If("[Monitor] 收到消息 主题: %s, 回复: %s, 数据: %s", msg.Subject, msg.Reply, string(msg.Data))
	})
	connect.Subscribe(TopicTrackStart, func(msg *nats.Msg) {
		logger.If("[Monitor] 收到消息 主题: %s, 回复: %s, 数据: %s", msg.Subject, msg.Reply, string(msg.Data))
	})
	// connect.Subscribe(TopicTrackStop, func(msg *nats.Msg) {
	// 	logger.If("[Monitor] 收到消息 主题: %s, 回复: %s, 数据: %s", msg.Subject, msg.Reply, string(msg.Data))
	// })
	connect.Subscribe(TopicTrackDel, func(msg *nats.Msg) {
		logger.If("[Monitor] 收到消息 主题: %s, 回复: %s, 数据: %s", msg.Subject, msg.Reply, string(msg.Data))
	})
	// connect.Subscribe(TopicTrackData, func(msg *nats.Msg) {
	// 	// logger.If("[Monitor] 收到消息 主题: %s, 回复: %s, 数据: %s", msg.Subject, msg.Reply, string(msg.Data))
	// 	data := MsgTrackData{}
	// 	err = json.Unmarshal(msg.Data, &data)
	// 	if err != nil {
	// 		logger.Ef("无法解析数据: [%s] %v", string(msg.Data), err)
	// 		return
	// 	}
	// 	logger.If(data.ImgUrl)
	// })
	select {}
}
