package algorithm

import "fmt"

// 状态代码
const (
	CodeSuccess              string = "0"
	CodeParamError           string = "-100"
	CodeTrackNotExist        string = "-1000"
	CodeTrackAlreadyExist    string = "-1001"
	CodeTrackZoneNotExist    string = "-1003"
	CodeTrackVideoOpenFailed string = "-1004"
	CodeTrackVideoReadFailed string = "-1005"
	CodeTrackVideoFinish     string = "-1006"
)

// MQ消息主题常量
const (
	TopicHeartbeat   string = "msg_heartbeat"    // 心跳（应用端/算法端/预警端）
	TopicTrackAdd    string = "msg_track_add"    // 新增视频追踪，文件或视频流（应用端->算法端）
	TopicTrackDel    string = "msg_track_del"    // 删除视频追踪，文件或视频流（应用端->算法端）
	TopicTrackSnap   string = "msg_track_snap"   // 视频追踪截图，文件或视频流（应用端->算法端）
	TopicTrackCfg    string = "msg_track_cfg"    // 配置视频追踪参数（应用端->算法端）
	TopicTrackStart  string = "msg_track_start"  // 开始视频追踪（应用端->算法端）
	TopicTrackStop   string = "msg_track_stop"   // 结束视频追踪（应用端->算法端）
	TopicTrackFocus  string = "msg_track_focus"  // 聚焦视频追踪（应用端->算法端）
	TopicTrackPause  string = "msg_track_pause"  // 暂停视频追踪（应用端->算法端）
	TopicTrackResume string = "msg_track_resume" // 恢复视频追踪（应用端->算法端）
	TopicTrackReset  string = "msg_track_reset"  // 清除所有任务（应用端->算法端）
	TopicTrackSync   string = "msg_track_sync"   // 同步所有任务（应用端->算法端）
	TopicTrackData   string = "msg_track_data"   // 发布视频追踪数据（算法端->预警端）
	TopicTrackStat   string = "msg_track_stat"   // 发布视频追踪状态（算法端->应用端）
)

// MsgHeartbeat 心跳消息
type MsgHeartbeat struct {
	SIP       string `json:"sip"`       // 本端IP
	Service   string `json:"service"`   // 本端服务：算法服务填SF、预警服务填YJ
	StartTime int64  `json:"starttime"` // 启动时间戳（毫秒）
	SysTime   int64  `json:"systime"`   // 系统时间戳（毫秒）
}

// MsgTrackAdd 添加追踪任务消息
type MsgTrackAdd struct {
	CID           string `json:"cid"`             // 视频唯一ID，如摄像机国标ID、文件测试ID
	SIP           string `json:"sip"`             // 发送端IP地址
	RIP           string `json:"rip"`             // 接收端IP地址
	LocalFilePath string `json:"local_file_path"` // 本地文件地址，用于测试
	PlatIp        string `json:"plat_ip"`         // 视频平台地址
	PlatPort      string `json:"plat_port"`       // 视频平台端口
}

// MsgTrackDel 删除追踪任务消息
type MsgTrackDel struct {
	CID string `json:"cid"` // 视频唯一ID
	SIP string `json:"sip"` // 发送端IP地址
	RIP string `json:"rip"` // 接收端IP地址
}

// MsgTrackSnap 截图消息
type MsgTrackSnap struct {
	CID string `json:"cid"` // 视频唯一ID
	SIP string `json:"sip"` // 发送端IP地址
	RIP string `json:"rip"` // 接收端IP地址
}

// MsgTrackCfg 配置追踪任务消息
type MsgTrackCfg struct {
	CID       string      `json:"cid"`
	SIP       string      `json:"sip"`
	RIP       string      `json:"rip"`
	ImageSize []int       `json:"imagesize"` // 图像宽高[width,height]
	Zones     []TrackZone `json:"zones"`     // 人工标注区域，必须标注
}

// TrackZone 追踪区域定义
type TrackZone struct {
	ZID           string      `json:"zid"`            // 区域唯一ID
	Direction     int         `json:"direction"`      // 车流方向，0不限制 1单向下 -1单向上
	XYXY          []float32   `json:"xyxy"`           // 区域顶点坐标（相对图像宽高转换成百分比）
	LaneDirection int         `json:"lane_direction"` // 车流方向 0不限制 1单向下 -1单向上
	LaneType      int         `json:"lane_type"`      // 车道类型 1.直行 2.左转 3.右转 4.直行加左转 5.直行加右转
	LaneNumber    int         `json:"lane_number"`
	Congestion    interface{} `json:"congestion,omitempty" algo:"congestion"`   // 拥堵算法配置
	Retrograde    interface{} `json:"retrograde,omitempty" algo:"retrograde"`   // 逆行算法配置
	CenterStop    interface{} `json:"center_stop,omitempty" algo:"center_stop"` // 中停算法配置
	Intrusion     interface{} `json:"intrusion,omitempty" algo:"intrusion"`     // 入侵检测算法配置
}

// MsgTrackStart 开始追踪消息
type MsgTrackStart struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

// MsgTrackStop 停止追踪消息
type MsgTrackStop struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

// MsgTrackFocus 聚焦追踪消息
type MsgTrackFocus struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

// MsgTrackPause 暂停追踪消息
type MsgTrackPause struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

// MsgTrackResume 恢复追踪消息
type MsgTrackResume struct {
	CID string `json:"cid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

// MsgTrackReset 重置所有任务消息
type MsgTrackReset struct {
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

// MsgTrackSync 同步所有任务消息
type MsgTrackSync struct {
	MID string `json:"mid"`
	SIP string `json:"sip"`
	RIP string `json:"rip"`
}

// MsgTrackStat 异步状态消息
type MsgTrackStat struct {
	CID  string `json:"cid"`
	Code string `json:"code"` // 状态代码
	Msg  string `json:"msg"`  // 状态信息
	Data string `json:"data"` // 按实际需要扩展，内容为json字符串
}

// MsgTrackResp 请求的直接响应消息
type MsgTrackResp struct {
	Code string      `json:"code"` // 状态代码
	Msg  string      `json:"msg"`  // 状态信息
	Data interface{} `json:"data"` // 按实际需要扩展，内容为json字符串
}

type MsgTrackData struct {
	CID    string `json:"cid"`
	T      int64  `json:"t"`       // 跟踪时间戳（毫秒）
	L      int    `json:"l"`       // 信号灯状态：0未知，1红灯，2绿灯
	ImgUrl string `json:"img_url"` // 图片URL
}

// HasValidConfig 检查该区域是否附加了任何有效的算法配置。
func (tz *TrackZone) HasValidConfig() bool {
	return tz.Congestion != nil || tz.Retrograde != nil || tz.CenterStop != nil || tz.Intrusion != nil
}

func (resp *MsgTrackResp) CheckSuccess() error {
	if resp.Code != CodeSuccess {
		return fmt.Errorf("operation failed: code: %s, msg: %s", resp.Code, resp.Msg)
	}
	return nil
}
