package algorithm

import (
	"context"
	"encoding/json"
	"fmt"
	"stvpp/internal/common"
	"sync"
	"time"

	"github.com/iWay7/go-common/misc"
)

const (
	// RebalanceThreshold 定义了触发重新平衡的负载不平衡比率。
	// 例如，如果设置为 0.2，当一个节点的负载比平均负载高出20%时，
	// 并且存在比平均负载低20%的节点时，可能会触发重新平衡。
	RebalanceThreshold = 0.2
)

// TrackerManager 视频追踪管理器
type TrackerManager struct {
	mu                sync.RWMutex
	rebalanceMu       sync.Mutex               // 确保同一时间只有一个重平衡任务在运行
	trackers          map[string]*VideoTracker // CID -> VideoTracker
	mqClient          common.MQClient
	ctx               context.Context
	cancel            context.CancelFunc
	statUpdateHandler func(vt *VideoTracker)
}

// NewTrackerManager 创建追踪管理器
func NewTrackerManager(mqClient common.MQClient) *TrackerManager {
	ctx, cancel := context.WithCancel(context.Background())
	return &TrackerManager{
		trackers: make(map[string]*VideoTracker),
		mqClient: mqClient,
		ctx:      ctx,
		cancel:   cancel,
	}
}

// SetStatUpdateHandler 设置状态更新处理器。
func (tm *TrackerManager) SetStatUpdateHandler(handler func(vt *VideoTracker)) {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	tm.statUpdateHandler = handler
}

// Start 启动管理器
func (tm *TrackerManager) Start() error {
	// 注册节点事件处理器
	misc.RegisterEventReceiver(func(event string, data interface{}) {
		switch event {
		case EventNodeAdded:
			go tm.handleRebalance(false)
		case EventNodeOffline:
			go tm.handleRebalance(true)
		}
	})

	misc.ScheduleTask(misc.Now().Add(time.Second*30).Unix(), func() int64 {
		PrintTrackerStatus(tm)
		return misc.Now().Add(time.Second * 10).Unix()
	})
	return nil
}

// Stop 停止管理器
func (tm *TrackerManager) Stop() {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	tm.cancel()
	tm.trackers = make(map[string]*VideoTracker)
	trackerLogger.Appendf("I: 追踪管理器: 已停止")
}

// AddTracker 添加追踪器
func (tm *TrackerManager) AddTracker(msg MsgTrackAdd) error {
	// 1. 选择节点并准备消息
	selectedNode, err := nodeManager.SelectNextNode()
	if err != nil {
		return fmt.Errorf("cannot add tracker: %v", err)
	}

	// 2. 预先检查追踪器是否存在，避免不必要的远程操作
	// 使用读锁以允许并发检查
	tm.mu.RLock()
	if _, exists := tm.trackers[msg.CID]; exists {
		tm.mu.RUnlock()
		return fmt.Errorf("tracker already exists: %s", msg.CID)
	}
	tm.mu.RUnlock()

	// 3. 创建追踪器实例，但此时不添加到管理器
	tracker := &VideoTracker{
		CID:           msg.CID,
		NodeID:        selectedNode.NodeID,
		LocalFilePath: msg.LocalFilePath,
		PlatIp:        msg.PlatIp,
		PlatPort:      msg.PlatPort,
		Status:        StatusIdle,
		mqClient:      tm.mqClient,
		onDelete:      tm.removeTracker,
		onStatUpdate:  tm.statUpdateHandler,
	}

	// 4. 尝试在远程节点上添加追踪器（耗时操作）
	if err := tracker.Add(); err != nil {
		// 如果远程添加失败，直接返回错误，因为追踪器还未加入管理器，无需清理
		return err
	}

	// 5. 远程添加成功后，再将其加入到管理器
	tm.mu.Lock()
	defer tm.mu.Unlock()

	// 再次检查，以处理在步骤2的读锁释放和当前写锁获取之间的并发请求（竞争条件）
	if _, exists := tm.trackers[msg.CID]; exists {
		// 异步执行删除，避免阻塞当前请求
		go func() {
			// 另一个并发请求先完成了。我们需要撤销刚刚在远程节点上创建的追踪器。
			trackerLogger.Appendf("W: 追踪管理器: 追踪器 '%s' 重复添加。将尝试删除远程实例。", msg.CID)
			err := tracker.Delete()
			if err != nil {
				trackerLogger.Appendf("E: 追踪管理器: 无法删除重复创建的远程追踪器 '%s': %v", msg.CID, err)
			}
		}()
		return fmt.Errorf("tracker race condition: %s already exists", msg.CID)
	}

	tm.trackers[msg.CID] = tracker
	trackerLogger.Appendf("I: 追踪管理器: 将追踪器 '%s' 添加到节点 '%s' 成功。", tracker.CID, tracker.NodeID)
	return nil
}

// ConfigureTracker 配置追踪器
func (tm *TrackerManager) ConfigureTracker(msg MsgTrackCfg) error {
	tm.mu.RLock()
	tracker, exists := tm.trackers[msg.CID]
	tm.mu.RUnlock()
	if !exists {
		return fmt.Errorf("tracker not found: %s", msg.CID)
	}
	return tracker.Configure(msg)
}

// StartTracker 启动追踪器
func (tm *TrackerManager) StartTracker(cid string) error {
	tm.mu.RLock()
	tracker, exists := tm.trackers[cid]
	tm.mu.RUnlock()
	if !exists {
		return fmt.Errorf("tracker not found for start: %s", cid)
	}
	return tracker.Start()
}

// SnapTracker 截图
func (tm *TrackerManager) SnapTracker(cid string) error {
	tm.mu.RLock()
	tracker, exists := tm.trackers[cid]
	tm.mu.RUnlock()
	if !exists {
		return fmt.Errorf("tracker not found: %s", cid)
	}
	return tracker.Snap()
}

// FocusTracker 聚焦
func (tm *TrackerManager) FocusTracker(cid string) error {
	tm.mu.RLock()
	tracker, exists := tm.trackers[cid]
	tm.mu.RUnlock()
	if !exists {
		return fmt.Errorf("tracker not found: %s", cid)
	}
	return tracker.Focus()
}

// PauseTracker 暂停
func (tm *TrackerManager) PauseTracker(cid string) error {
	tm.mu.RLock()
	tracker, exists := tm.trackers[cid]
	tm.mu.RUnlock()
	if !exists {
		return fmt.Errorf("tracker not found: %s", cid)
	}
	return tracker.Pause()
}

// ResumeTracker 恢复
func (tm *TrackerManager) ResumeTracker(cid string) error {
	tm.mu.RLock()
	tracker, exists := tm.trackers[cid]
	tm.mu.RUnlock()
	if !exists {
		return fmt.Errorf("tracker not found: %s", cid)
	}
	return tracker.Resume()
}

// StopTracker 停止追踪器
func (tm *TrackerManager) StopTracker(cid string) error {
	tm.mu.RLock()
	tracker, exists := tm.trackers[cid]
	tm.mu.RUnlock()
	if !exists {
		return nil // 不存在，无需操作
	}
	return tracker.Stop()
}

// DeleteTracker 删除追踪器
func (tm *TrackerManager) DeleteTracker(cid string) error {
	tm.mu.RLock()
	tracker, exists := tm.trackers[cid]
	tm.mu.RUnlock()
	if !exists {
		return nil // 不是错误，只是无事可做。
	}
	return tracker.Delete()
}

// DeleteTrackerAnyway 删除追踪器（从所有节点上）
func (tm *TrackerManager) DeleteTrackerAnyway(cid string) error {
	tm.mu.RLock()
	tracker, exists := tm.trackers[cid]
	tm.mu.RUnlock()
	if exists {
		return tracker.Delete()
	}
	// 从所有节点中删除此追踪器
	aliveNodes := nodeManager.GetAliveNodes()
	var wg sync.WaitGroup
	errChan := make(chan error, len(aliveNodes))

	for _, node := range aliveNodes {
		wg.Add(1)
		go func(nodeID string) {
			defer wg.Done()
			msg := &MsgTrackDel{
				CID: cid,
				SIP: common.GetLocalIP(),
				RIP: nodeID,
			}
			resp, err := requestTrackResp(tm.mqClient, TopicTrackDel, msg, 180*time.Second, false)
			if err == nil {
				if resp.Code != CodeTrackNotExist {
					err = resp.CheckSuccess()
				}
			}
			if err != nil {
				err = fmt.Errorf("向节点 '%s' 发送 DEL 请求失败: %v", nodeID, err)
				trackerLogger.Appendf("E: %v", err)
				errChan <- err
			}
		}(node.NodeID)
	}
	wg.Wait()
	close(errChan)
	if err := <-errChan; err != nil {
		return err
	}
	return nil
}

// Reset 同步重置追踪器。
func (tm *TrackerManager) Reset() error {
	// 并行、同步地重置所有节点
	aliveNodes := nodeManager.GetAliveNodes()
	if len(aliveNodes) == 0 {
		trackerLogger.Appendf("W: 追踪管理器: 无活动节点，无需重置。")
		return nil
	}

	var wg sync.WaitGroup
	errChan := make(chan error, len(aliveNodes))

	for _, node := range aliveNodes {
		wg.Add(1)
		go func(nodeID string) {
			defer wg.Done()
			msg := &MsgTrackReset{
				SIP: common.GetLocalIP(),
				RIP: nodeID,
			}
			_, err := requestTrackResp(tm.mqClient, TopicTrackReset, msg, 180*time.Second, true)
			if err != nil {
				err = fmt.Errorf("向节点 '%s' 发送 RESET 请求失败: %v", nodeID, err)
				trackerLogger.Appendf("E: %v", err)
				errChan <- err
			}
		}(node.NodeID)
	}
	wg.Wait()
	close(errChan)

	// 检查是否有任何 goroutine 出错
	if err := <-errChan; err != nil {
		return err // 返回第一个遇到的错误
	}

	// 所有节点的重置请求都成功后，清空本地追踪器列表
	tm.mu.Lock()
	tm.trackers = make(map[string]*VideoTracker)
	tm.mu.Unlock()
	trackerLogger.Appendf("I: 追踪管理器: 已成功重置所有活动节点[%v]，并已清理本地追踪器列表。", len(aliveNodes))

	return nil
}

// Sync 向所有节点发送一个同步通知
func (tm *TrackerManager) Sync() error {
	aliveNodes := nodeManager.GetAliveNodes()
	var wg sync.WaitGroup
	errChan := make(chan error, len(aliveNodes))

	for _, node := range aliveNodes {
		wg.Add(1)
		go func(nodeID string) {
			defer wg.Done()
			msg := &MsgTrackSync{
				SIP: common.GetLocalIP(),
				RIP: nodeID,
			}
			reply, err := tm.mqClient.Publish(TopicTrackSync, msg)
			if err != nil {
				trackerLogger.Appendf("E: 同步追踪器: 向节点 '%s' 发送同步请求失败: %v", nodeID, err)
				errChan <- err
			}
			err = tm.mqClient.Subscribe(reply, tm.onTrackerSync)
			if err != nil {
				trackerLogger.Appendf("E: 同步追踪器: 订阅同步响应失败: %v", err)
				errChan <- err
			}
		}(node.NodeID)
	}
	wg.Wait()
	close(errChan)

	// 检查是否有任何 goroutine 出错
	if err := <-errChan; err != nil {
		return err // 返回第一个遇到的错误
	}
	return nil
}

// GetTrackerInfo 获取追踪器信息
func (tm *TrackerManager) GetTrackerInfo(cid string) *VideoTracker {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	tracker, _ := tm.trackers[cid]
	return tracker
}

// GetTrackerImgUrl 获取追踪器的图片URL
func (tm *TrackerManager) GetTrackerImgUrl(cid string, imgTimeout time.Duration) (string, error) {
	tm.mu.RLock()
	tracker, exists := tm.trackers[cid]
	if !exists {
		tm.mu.RUnlock()
		return "", fmt.Errorf("tracker not found: %s", cid)
	}
	tm.mu.RUnlock()

	return tracker.GetImgUrl(imgTimeout)
}

func (tm *TrackerManager) GetTrackerZone(cid, zid string) (TrackZone, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	tracker, exists := tm.trackers[cid]
	if !exists {
		return TrackZone{}, fmt.Errorf("tracker not found: %s", cid)
	}
	return tracker.GetZone(zid)
}

// GetAllTrackers 返回所有追踪器的切片
func (tm *TrackerManager) GetAllTrackers() []*VideoTracker {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	trackers := make([]*VideoTracker, 0, len(tm.trackers))
	for _, tracker := range tm.trackers {
		trackers = append(trackers, tracker)
	}
	return trackers
}

func (tm *TrackerManager) onTrackerSync(msg *common.Msg) {
	type syncResp struct {
		CID    string `json:"cid"`
		ImgUrl string `json:"img_url"`
	}
	resp := &MsgTrackResp{
		Data: &syncResp{},
	}
	err := json.Unmarshal(msg.Data, resp)
	if err != nil {
		trackerLogger.Appendf("W: 追踪管理器: 无法解析同步消息: %v", err)
		return
	}
	syncMsg := resp.Data.(*syncResp)
	if syncMsg.CID == "" || syncMsg.ImgUrl == "" {
		return
	}
	tm.mu.Lock()
	defer tm.mu.Unlock()
	if tracker, exists := tm.trackers[syncMsg.CID]; exists {
		tracker.ImgUrl = syncMsg.ImgUrl
		if tm.statUpdateHandler != nil {
			tm.statUpdateHandler(tracker)
		}
	}
}

// removeTracker 从管理器中移除一个追踪器。在 DEL 或 RESET 操作后使用。
func (tm *TrackerManager) removeTracker(cid string) {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if _, exists := tm.trackers[cid]; exists {
		delete(tm.trackers, cid)
		trackerLogger.Appendf("I: 追踪管理器: 已从管理器中删除追踪器 '%s'。", cid)
	}
}

// handleRebalance 是统一的重平衡处理器。
// force=true 表示无论如何都要执行重平衡（例如节点离线）。
// force=false 表示仅当负载不平衡超过阈值时才执行重平衡。
func (tm *TrackerManager) handleRebalance(force bool) {
	tm.rebalanceMu.Lock()
	defer tm.rebalanceMu.Unlock()

	allTrackers := tm.GetAllTrackers()
	if len(allTrackers) == 0 {
		return // 没有追踪器，无需平衡
	}

	aliveNodes := nodeManager.GetAliveNodes()
	if len(aliveNodes) == 0 {
		trackerLogger.Appendf("W: 重平衡: 没有可用的活动节点。")
		// 清除本地追踪器列表
		tm.mu.Lock()
		tm.trackers = make(map[string]*VideoTracker)
		tm.mu.Unlock()
		return
	}

	// 1. 分类追踪器：必须移动的（在离线节点上）和在活动节点上的
	var trackersToMove []*VideoTracker
	trackersOnAliveNodes := make(map[string][]*VideoTracker)
	for _, node := range aliveNodes {
		trackersOnAliveNodes[node.NodeID] = []*VideoTracker{}
	}

	for _, tracker := range allTrackers {
		if !nodeManager.IsNodeAlive(tracker.NodeID) {
			trackersToMove = append(trackersToMove, tracker)
		} else {
			trackersOnAliveNodes[tracker.NodeID] = append(trackersOnAliveNodes[tracker.NodeID], tracker)
		}
	}

	// 2. 决定是否需要重平衡
	shouldRebalance := force || len(trackersToMove) > 0

	if !shouldRebalance && len(aliveNodes) > 1 {
		// 如果不是强制的，检查负载是否超过阈值
		nodeCount := len(aliveNodes)
		totalTrackersOnAliveNodes := len(allTrackers) - len(trackersToMove)
		avgTrackers := float64(totalTrackersOnAliveNodes) / float64(nodeCount)

		maxTrackers := 0
		minTrackers := totalTrackersOnAliveNodes
		for _, trackers := range trackersOnAliveNodes {
			count := len(trackers)
			if count > maxTrackers {
				maxTrackers = count
			}
			if count < minTrackers {
				minTrackers = count
			}
		}

		upperBound := avgTrackers * (1 + RebalanceThreshold)
		lowerBound := avgTrackers * (1 - RebalanceThreshold)

		if float64(maxTrackers) > upperBound && float64(minTrackers) < lowerBound {
			trackerLogger.Appendf("I: 重平衡: 检测到节点负载不平衡 (最大: %d, 最小: %d, 平均: %.2f)，超过阈值 (%.2f)。",
				maxTrackers, minTrackers, avgTrackers, RebalanceThreshold)
			shouldRebalance = true
		}
	}

	if !shouldRebalance {
		trackerLogger.Appendf("I: 重平衡: 节点负载在阈值范围内，无需移动追踪器。")
		return
	}

	trackerLogger.Appendf("I: 重平衡: 开始执行。")

	// 3. 计算目标分布
	nodeCount := len(aliveNodes)
	totalTrackers := len(allTrackers)
	avgTrackers := totalTrackers / nodeCount
	remainder := totalTrackers % nodeCount

	targetCounts := make(map[string]int)
	for i, node := range aliveNodes {
		targetCounts[node.NodeID] = avgTrackers
		if i < remainder {
			targetCounts[node.NodeID]++
		}
	}

	// 4. 从过载的节点收集多余的追踪器
	for nodeID, trackers := range trackersOnAliveNodes {
		targetCount := targetCounts[nodeID]
		currentCount := len(trackers)
		if currentCount > targetCount {
			overload := currentCount - targetCount
			trackersToMove = append(trackersToMove, trackers[:overload]...)
			trackersOnAliveNodes[nodeID] = trackers[overload:] // 更新该节点的追踪器列表
		}
	}

	if len(trackersToMove) == 0 {
		trackerLogger.Appendf("I: 重平衡: 无需移动追踪器，平衡完成。")
		return
	}
	trackerLogger.Appendf("I: 重平衡: 计划移动 %d 个追踪器以达到平衡。", len(trackersToMove))

	// 5. 将追踪器分配给欠载的节点
	moveIndex := 0
	for _, node := range aliveNodes {
		targetCount := targetCounts[node.NodeID]
		currentCount := len(trackersOnAliveNodes[node.NodeID])

		if currentCount < targetCount {
			needed := targetCount - currentCount
			for i := 0; i < needed && moveIndex < len(trackersToMove); i++ {
				trackerToMove := trackersToMove[moveIndex]
				moveIndex++

				if trackerToMove.NodeID == node.NodeID {
					i--
					continue
				}

				trackerLogger.Appendf("I: 重平衡: 正在移动追踪器 '%s' 从节点 '%s' 到 '%s'", trackerToMove.CID, trackerToMove.NodeID, node.NodeID)
				err := tm.restartTracker(trackerToMove, node.NodeID)
				if err != nil {
					trackerLogger.Appendf("E: 重平衡: 无法在节点 '%s' 上重启追踪器 '%s': %v", node.NodeID, trackerToMove.CID, err)
				}
			}
		}
	}
	trackerLogger.Appendf("I: 重平衡: 过程完成。")
}

// restartTracker 停止一个追踪器并在新的节点上重启它。
func (tm *TrackerManager) restartTracker(tracker *VideoTracker, newNodeID string) error {
	oldNodeID := tracker.NodeID
	if oldNodeID == newNodeID {
		return nil // 无需移动
	}

	trackerLogger.Appendf("I: 追踪管理器: 准备将追踪器 '%s' 从节点 '%s' 重启到新节点 '%s'", tracker.CID, oldNodeID, newNodeID)

	// 1. 保存旧状态
	tracker.mu.RLock()
	wasConfigured := tracker.Status == StatusConfigured || tracker.Status == StatusRunning
	wasRunning := tracker.Status == StatusRunning
	originalZones := tracker.Zones
	originalImageSize := tracker.ImageSize
	tracker.mu.RUnlock()

	// 更新tracker的节点ID，然后执行Add
	tracker.mu.Lock()
	tracker.NodeID = newNodeID
	tracker.mu.Unlock()

	if err := tracker.Add(); err != nil {
		trackerLogger.Appendf("E: 追踪管理器: 在新节点 '%s' 上为追踪器 '%s' 执行 Add 失败: %v", newNodeID, tracker.CID, err)
		// 尝试将节点ID恢复为旧ID，但这可能不是最佳策略
		tracker.mu.Lock()
		tracker.NodeID = oldNodeID
		tracker.mu.Unlock()
		return err
	}

	// 3. 如果之前已配置，重新发送配置
	if wasConfigured {
		if err := tracker.Configure(MsgTrackCfg{Zones: originalZones, ImageSize: originalImageSize}); err != nil {
			trackerLogger.Appendf("E: 追踪管理器: 为追踪器 '%s' 执行 Cfg 失败: %v", tracker.CID, err)
			// 尝试在新节点上删除它，因为它处于不一致的状态
			_ = tm.DeleteTracker(tracker.CID)
			return err
		}
	}

	// 4. 如果之前在运行，重新启动
	if wasRunning {
		if err := tracker.Start(); err != nil {
			trackerLogger.Appendf("E: 追踪管理器: 为追踪器 '%s' 执行 Start 失败: %v", tracker.CID, err)
			// 尝试在新节点上删除它
			_ = tm.DeleteTracker(tracker.CID)
			return err
		}
	}

	trackerLogger.Appendf("I: 追踪管理器: 成功将追踪器 '%s' 重启到新节点 '%s'。", tracker.CID, newNodeID)

	// 5. 向旧节点发送删除指令（如果它还活着）
	if oldNodeID != "" && nodeManager.IsNodeAlive(oldNodeID) {
		trackerLogger.Appendf("I: 追踪管理器: 正在向旧节点 '%s' 发送删除追踪器 '%s' 的命令。", oldNodeID, tracker.CID)
		// 这里我们使用一个新的临时tracker对象来发送删除命令，以避免锁定和状态冲突
		tempTracker := &VideoTracker{mqClient: tm.mqClient, CID: tracker.CID, NodeID: oldNodeID}
		if err := tempTracker.Delete(); err != nil {
			// 记录错误但不要中止，因为新追踪器已在运行
			trackerLogger.Appendf("W: 追踪管理器: 向旧节点 '%s' 发送删除追踪器 '%s' 的命令失败: %v", oldNodeID, tracker.CID, err)
		}
	}

	return nil
}
