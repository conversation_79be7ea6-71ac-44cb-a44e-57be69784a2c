package modules

import (
	"stvpp/internal/common"
)

var ewcpUtil *EarlyWarningConfigProviderUtil

func init() {
	ewcpUtil = &EarlyWarningConfigProviderUtil{
		ProviderUtil: common.NewProviderUtil[EarlyWarningConfigProvider](),
	}
}

func GetEWCPUtil() *EarlyWarningConfigProviderUtil {
	return ewcpUtil
}

type EarlyWarningConfigProviderUtil struct {
	common.ProviderUtil[EarlyWarningConfigProvider]
}

type EarlyWarningConfigProvider interface {
	common.Provider
	Save(taskId int64, cfg interface{}) error
	DeleteByTaskId(taskId int64) error
}

func registerEarlyWarningConfigProvider(tag string, provider EarlyWarningConfigProvider) {
	ewcpUtil.RegisterProvider(tag, &provider)
}

type ExcludeFrequency struct {
	Seconds int `json:"seconds"`
	Count   int `json:"count"`
}

type ForceWarning struct {
	Anyways  bool `json:"anyways,omitempty"`
	Multiple int  `json:"multiple"`
}

type EarlyWarningModule struct {
	Id               int64             `json:"id" xorm:"pk autoincr bigint"`
	TaskId           int64             `json:"task_id" xorm:"index not null bigint"`
	Enabled          bool              `json:"enabled" xorm:"not null bool"`
	ExcludePeriod    *Period           `json:"exclude_period" xorm:"not null json"`
	ExcludeFrequency *ExcludeFrequency `json:"exclude_frequency" xorm:"not null json"`
}
