package modules

import (
	"fmt"

	"github.com/iWay7/go-common/db"
	"github.com/iWay7/go-common/misc"
	"xorm.io/xorm"
)

type PointDao struct {
	db.BaseDao
}

var pointDao *PointDao

func initPointDao() {
	pointDao = new(PointDao)
	pointDao.initTables()
}

func GetPointDao() *PointDao {
	return pointDao
}

type Point struct {
	Id          int64        `json:"id" xorm:"pk autoincr bigint"`
	Name        string       `json:"name" xorm:"not null varchar(255)"`
	Type        int          `json:"type" xorm:"not null int"`
	SecType     int          `json:"sec_type" xorm:"not null int"`
	Area        string       `json:"area" xorm:"not null char(6)"`
	Key         bool         `json:"key" xorm:"not null bool"`
	ImgPath     string       `json:"img_path" xorm:"not null varchar(255)"`
	Boundary    *GeomPolygon `json:"boundary" xorm:"not null"`
	Description string       `json:"description" xorm:"varchar(255)"`
	CreatedAt   int64        `json:"created_at" xorm:"index created"`
	UpdatedAt   int64        `json:"updated_at" xorm:"index updated"`

	Relations []*PointVideoRelation `json:"-" xorm:"-"`
}

type PointVideoRelation struct {
	Id        int64      `json:"id" xorm:"pk autoincr bigint"`
	PointId   int64      `json:"point_id" xorm:"index not null bigint"`
	VideoId   int64      `json:"video_id" xorm:"index not null bigint"`
	VideoCode string     `json:"video_code" xorm:"index not null varchar(255)"`
	Point     *GeomPoint `json:"point" xorm:"not null"`
	CreatedAt int64      `json:"created_at" xorm:"index created"`
	UpdatedAt int64      `json:"updated_at" xorm:"index updated"`
}

func (p *Point) TableName() string {
	return "base_point"
}

func (pvr *PointVideoRelation) TableVersion() int {
	return 1
}

func (pvr *PointVideoRelation) TableName() string {
	return "base_point_video_relation"
}

func (pd *PointDao) FindByFilter(name, videoCode string) ([]*Point, error) {
	var result []*Point
	where := ""
	if name != "" {
		where += "name like '%" + name + "%'"
	}
	err := db.NewSession().Where(where).Find(&result)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().Where(where).Find(&result): %v", err)
	}
	if videoCode != "" {
		var pvr []*PointVideoRelation
		err = db.NewSession().Cols("point_id").Where("video_code like '%" + videoCode + "%'").Find(&pvr)
		if err != nil {
			return nil, fmt.Errorf("db.NewSession().Where(\"video_code = ?\", videoCode).Find(&pvr): %v", err)
		}
		var ids []int64
		for _, r := range pvr {
			ids = append(ids, r.PointId)
		}
		filtered := make([]*Point, 0, len(result))
		for _, r := range result {
			if misc.Int64ArrayContains(ids, r.Id) {
				filtered = append(filtered, r)
			}
		}
		return filtered, nil
	}
	return result, err
}

func (pd *PointDao) GetById(id int64) (*Point, error) {
	var result Point
	got, err := db.NewSession().ID(id).Get(&result)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().ID(id).Get(&result): %v", err)
	}
	if !got {
		return nil, nil
	}
	return &result, nil
}

func (pd *PointDao) FindRelationMapById(id int64) (map[int64]*PointVideoRelation, []int64, error) {
	var relations []*PointVideoRelation
	err := db.NewSession().Where("point_id = ?", id).Find(&relations)
	if err != nil {
		return nil, nil, fmt.Errorf("db.NewSession().Where(\"point_id = ?\", id).Find(&relations): %v", err)
	}
	var videoIds []int64
	for _, regionVideo := range relations {
		videoIds = append(videoIds, regionVideo.VideoId)
	}
	relationsMap := make(map[int64]*PointVideoRelation)
	for _, relation := range relations {
		relationsMap[relation.VideoId] = relation
	}
	return relationsMap, videoIds, nil
}

func (pd *PointDao) FindVideoIdsInPointIds(ids []int64) (map[int64][]int64, error) {
	var relations []*PointVideoRelation
	err := db.NewSession().Cols("point_id", "video_id").In("point_id", ids).Find(&relations)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().Cols(\"point_id\", \"video_id\").In(\"point_id\", ids).Find(&relations): %v", err)
	}
	videoIdMap := make(map[int64][]int64)
	for _, relation := range relations {
		videoIdMap[relation.PointId] = append(videoIdMap[relation.PointId], relation.VideoId)
	}
	return videoIdMap, nil
}

func (pd *PointDao) Add(p *Point) error {
	return db.DoTransaction(func(session *xorm.Session) error {
		_, err := session.Insert(p)
		if err != nil {
			return fmt.Errorf("session.Insert(p): %v", err)
		}
		id := p.Id
		relations := p.Relations
		for _, relation := range relations {
			relation.PointId = id
		}
		err = pd.AddPointVideoRelations(relations)
		if err != nil {
			return err
		}
		return nil
	})
}

func (pd *PointDao) AddPointVideoRelations(pvrs []*PointVideoRelation) error {
	_, err := db.GetWithTransactionOrNewSession().Insert(pvrs)
	if err != nil {
		return fmt.Errorf("db.GetWithTransactionOrNewSession().Insert(pvrs): %v", err)
	}
	return nil
}

func (pd *PointDao) Update(p *Point) error {
	return db.DoTransaction(func(session *xorm.Session) error {
		_, err := session.ID(p.Id).AllCols().Update(p)
		if err != nil {
			return fmt.Errorf("session.ID(p.AlgorithmConfigId).AllCols().Update(p): %v", err)
		}
		relations := p.Relations
		_, err = session.Where("point_id = ?", p.Id).Delete(new(PointVideoRelation))
		if err != nil {
			return fmt.Errorf("session.Where(\"point_id = ?\", p.Id).Delete(new(PointVideoRelation)): %v", err)
		}
		for _, relation := range relations {
			relation.PointId = p.Id
		}
		err = pd.AddPointVideoRelations(relations)
		if err != nil {
			return err
		}
		return nil
	})
}

func (pd *PointDao) Delete(id int64) error {
	return db.DoTransaction(func(session *xorm.Session) error {
		_, err := session.ID(id).Delete(new(Point))
		if err != nil {
			return fmt.Errorf("session.ID(id).Delete(new(Point)): %v", err)
		}
		_, err = session.Where("point_id = ?", id).Delete(new(PointVideoRelation))
		if err != nil {
			return fmt.Errorf("session.Where(\"point_id = ?\", id).Delete(new(PointVideoRelation)): %v", err)
		}
		return nil
	})
}

func (pd *PointDao) initTables() {
	err := db.InitTable(new(Point), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(Point), nil): %v", err)
		panic(err)
	}
	err = db.InitTable(new(PointVideoRelation), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(PointVideoRelation), nil): %v", err)
		panic(err)
	}
}
