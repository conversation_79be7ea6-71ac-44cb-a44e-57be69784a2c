package modules

import (
	"fmt"

	"github.com/iWay7/go-common/db"
)

type VideoTrackerStatDao struct {
	db.BaseDao
}

var videoTrackerStatDao *VideoTrackerStatDao

func initVideoTrackerStatDao() {
	videoTrackerStatDao = new(VideoTrackerStatDao)
	videoTrackerStatDao.initTables()
}

func GetVideoTrackerStatDao() *VideoTrackerStatDao {
	return videoTrackerStatDao
}

type VideoTrackerStat struct {
	Id        int64  `json:"id" xorm:"pk autoincr bigint"`
	VideoId   int64  `json:"video_id" xorm:"index not null bigint"`
	NodeId    string `json:"node_id" xorm:"index not null varchar(255)"`
	Status    int    `json:"status" xorm:"not null int"`
	ImgUrl    string `json:"img_url" xorm:"varchar(255)"`
	CreatedAt int64  `json:"created_at" xorm:"index created"`
	UpdatedAt int64  `json:"updated_at" xorm:"index updated"`
}

func (vts *VideoTrackerStat) TableName() string {
	return "stat_video_tracker"
}

func (videoTrackerStatDao *VideoTrackerStatDao) Create(vt *VideoTrackerStat) error {
	_, err := db.NewSession().Insert(vt)
	return err
}

func (videoTrackerStatDao *VideoTrackerStatDao) GetLatestByVideoId(videoId int64) (*VideoTrackerStat, error) {
	var result VideoTrackerStat
	has, err := db.NewSession().Where("video_id = ?", videoId).OrderBy("id DESC").Limit(1).Get(&result)
	if err != nil {
		return nil, fmt.Errorf("failed to get latest video tracker stat: %v", err)
	}
	if !has {
		return nil, nil
	}
	return &result, nil
}

func (videoTrackerStatDao *VideoTrackerStatDao) GetImgUrlByVideoId(vid int64) string {
	video, err := videoDao.GetById(vid)
	if err != nil {
		return ""
	}
	if video == nil {
		return ""
	}
	cid := video.GbCode
	vt := trackerManager.GetTrackerInfo(cid)
	if vt != nil && vt.ImgUrl != "" {
		return vt.ImgUrl
	}
	var result VideoTrackerStat
	has, err := db.NewSession().Where("video_id = ?", vid).Cols("img_url").OrderBy("id DESC").Limit(1).Get(&result)
	if err != nil || !has {
		return ""
	}
	return result.ImgUrl
}

func (videoTrackerStatDao *VideoTrackerStatDao) initTables() {
	err := db.InitTable(new(VideoTrackerStat), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(VideoTrackerStat), nil): %v", err)
		panic(err)
	}
}
