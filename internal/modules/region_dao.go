package modules

import (
	"fmt"

	"github.com/iWay7/go-common/db"
	"xorm.io/xorm"
)

type RegionDao struct {
	db.BaseDao
}

var regionDao *RegionDao

func initRegionDao() {
	regionDao = new(RegionDao)
	regionDao.initTables()
}

func GetRegionDao() *RegionDao {
	return regionDao
}

type Region struct {
	Id            int64        `json:"id" xorm:"pk autoincr bigint"`
	VideoId       int64        `json:"video_id" xorm:"index not null bigint"`
	MarkRegion    *GeomPolygon `json:"mark_region" xorm:"not null"`
	Name          string       `json:"name" xorm:"not null varchar(255)"`
	Direction     int          `json:"direction" xorm:"not null int comment('车流方向: 0不限制 1单向下 -1单向上')"`
	LaneDirection int          `json:"lane_direction" xorm:"not null int"`
	LaneType      int          `json:"lane_type" xorm:"not null int comment('车道类型: 1.直行 2.左转 3.右转 4.直行加左转 5.直行加右转')"`
	LaneNumber    int          `json:"lane_number" xorm:"not null int"`
	CreatedAt     int64        `json:"created_at" xorm:"index created"`
	UpdatedAt     int64        `json:"updated_at" xorm:"index updated"`
}

type VideoAlgorithmConfig struct {
	*Region
	Congestion *CongestionAlgorithm `json:"congestion" algo:"congestion"`
	Retrograde *Retrograde          `json:"retrograde" algo:"retrograde"`
	CenterStop *CenterStop          `json:"center_stop" algo:"center_stop"`
	Intrusion  *Intrusion           `json:"intrusion" algo:"intrusion"`
}

func (r *Region) TableName() string {
	return "algo_region"
}

func (regionDao *RegionDao) FindByVideoId(videoId int64) ([]*Region, error) {
	var result []*Region
	err := db.NewSession().Where("video_id = ?", videoId).Find(&result)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().Where(\"video_id = ?\", videoId).Find(&result): %v", err)
	}
	return result, nil
}

// Save 使用事务来原子性地保存算法区域及其所有相关的算法配置
func (regionDao *RegionDao) Save(cfg *VideoAlgorithmConfig) error {
	ar := &Region{
		Id:            cfg.Id,
		VideoId:       cfg.VideoId,
		MarkRegion:    cfg.MarkRegion,
		Name:          cfg.Name,
		Direction:     cfg.Direction,
		LaneDirection: cfg.LaneDirection,
		LaneType:      cfg.LaneType,
		LaneNumber:    cfg.LaneNumber,
	}

	return db.DoTransaction(func(session *xorm.Session) error {
		if ar.Id == 0 {
			_, err := session.Insert(ar)
			if err != nil {
				return fmt.Errorf("db.NewSession().Insert(ar): %v", err)
			}
		} else {
			_, err := session.ID(ar.Id).AllCols().Update(ar)
			if err != nil {
				return fmt.Errorf("db.NewSession().ID(ar.AlgorithmConfigId).AllCols().Update(ar): %v", err)
			}
		}
		err := acpUtil.DoFunction(cfg, func(fv interface{}, provider AlgorithmConfigProvider) error {
			return provider.Save(ar, fv)
		}, true)
		if err != nil {
			return err
		}
		return nil
	})
}

func (regionDao *RegionDao) Delete(id int64) error {
	return db.DoTransaction(func(session *xorm.Session) error {
		_, err := db.NewSession().ID(id).Delete(new(Region))
		if err != nil {
			return fmt.Errorf("db.NewSession().ID(id).Delete(new(Region)): %v", err)
		}
		err = acpUtil.DoFunction(&VideoAlgorithmConfig{}, func(fv interface{}, provider AlgorithmConfigProvider) error {
			return provider.DeleteByAlgorithmRegionId(id)
		}, false)
		if err != nil {
			return fmt.Errorf("DoFunction(id:%v): %v", id, err)
		}
		return nil
	})
}

func (regionDao *RegionDao) initTables() {
	err := db.InitTable(new(Region), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(Region), nil): %v", err)
		panic(err)
	}
}
