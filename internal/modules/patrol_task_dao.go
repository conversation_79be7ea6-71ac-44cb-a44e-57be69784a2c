package modules

import (
	"fmt"
	"stvpp/internal/common"

	"github.com/iWay7/go-common/db"
	"github.com/iWay7/go-common/misc"
	"xorm.io/xorm"
)

type PatrolTaskDao struct {
	db.BaseDao
}

var patrolTaskDao *PatrolTaskDao

func initPatrolTaskDao() {
	patrolTaskDao = new(PatrolTaskDao)
	patrolTaskDao.initTables()
}

func GetPatrolTaskDao() *PatrolTaskDao {
	return patrolTaskDao
}

type PatrolTask struct {
	Id           int64  `json:"id" xorm:"pk autoincr bigint"`
	Name         string `json:"name" xorm:"not null varchar(255)"`
	Status       bool   `json:"status" xorm:"not null int default 0"`
	ScheduleType int    `json:"schedule_type" xorm:"not null int comment('计划类型: 1.周期性执行 2.一次性执行')"`
	Description  string `json:"description" xorm:"varchar(255)"`
	CreatedAt    int64  `json:"created_at" xorm:"index created"`
	UpdatedAt    int64  `json:"updated_at" xorm:"index updated"`
}

type PatrolTaskConfig struct {
	Id           int64  `json:"id"`
	Name         string `json:"name"`
	ScheduleType int    `json:"schedule_type"`
	Description  string `json:"description"`
	CreatedAt    int64  `json:"created_at"`
	UpdatedAt    int64  `json:"updated_at"`

	Schedules  []*TaskSchedule         `json:"-"`
	Relations  []*TaskPointRelation    `json:"-"`
	Congestion *CongestionEarlyWarning `json:"-" algo:"congestion"`
	Retrograde *RetrogradeEarlyWarning `json:"-" algo:"retrograde"`
	CenterStop *CenterStopEarlyWarning `json:"-" algo:"center_stop"`
	Intrusion  *IntrusionEarlyWarning  `json:"-" algo:"intrusion"`
}

type EnabledPatrolTaskConfig struct {
	Id           int64                `json:"id"`
	Name         string               `json:"name"`
	ScheduleType int                  `json:"schedule_type"`
	Schedules    []*TaskSchedule      `json:"schedules"`
	Relations    []*TaskPointRelation `json:"relations"`
}

type TaskSchedule struct {
	Id        int64           `json:"id" xorm:"pk autoincr bigint"`
	TaskId    int64           `json:"task_id" xorm:"index not null bigint"`
	AlgoType  common.AlgoType `json:"algo_type" xorm:"index not null int comment('算法类型: 0.态势检测 1.道路拥堵 2.逆行检测 3.中心停车 4.入侵检测')"`
	Periods   []*Period       `json:"periods" xorm:"not null json"`
	Status    bool            `json:"status" xorm:"not null int default 0"`
	CreatedAt int64           `json:"created_at" xorm:"index created"`
	UpdatedAt int64           `json:"updated_at" xorm:"index updated"`
}

type TaskPointRelation struct {
	Id        int64 `json:"id" xorm:"pk autoincr bigint"`
	TaskId    int64 `json:"task_id" xorm:"index not null bigint"`
	PointId   int64 `json:"point_id" xorm:"index not null bigint"`
	CreatedAt int64 `json:"created_at" xorm:"index created"`
	UpdatedAt int64 `json:"updated_at" xorm:"index updated"`
}

type Period struct {
	Keys  []int        `json:"keys,omitempty"`
	Range *PeriodRange `json:"range,omitempty"`
	Time  *PeriodTime  `json:"time"`
}

type PeriodRange struct {
	Begin string `json:"begin"`
	End   string `json:"end"`
}

type PeriodTime struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

func (patrolTask *PatrolTask) TableName() string {
	return "base_patrol_task"
}

func (taskSchedule *TaskSchedule) TableName() string {
	return "base_patrol_task_schedule"
}

func (patrolTaskPointRelation *TaskPointRelation) TableName() string {
	return "base_patrol_task_point_relation"
}

func (patrolTaskDao *PatrolTaskDao) FindByFilter(name, pointName string) ([]*PatrolTask, error) {
	var result []*PatrolTask
	where := ""
	if name != "" {
		where += "name like '%" + name + "%'"
	}
	err := db.NewSession().Where(where).Find(&result)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().Where(\"name like '?\", name).Find(&result): %v", err)
	}
	if pointName != "" {
		var relations []*TaskPointRelation
		err = db.NewSession().Cols("task_id").Where("point_id in (select id from base_point where name like '%" + pointName + "%')").Find(&relations)
		if err != nil {
			return nil, fmt.Errorf("db.NewSession().Cols(\"task_id\").Where(\"point_id in (select id from base_point where name like '%"+pointName+"%')\").Find(&relations): %v", err)
		}
		var ids []int64
		for _, relation := range relations {
			ids = append(ids, relation.TaskId)
		}
		filtered := make([]*PatrolTask, 0, len(result))
		for _, task := range result {
			if misc.Int64ArrayContains(ids, task.Id) {
				filtered = append(filtered, task)
			}
		}
		return filtered, nil
	}
	return result, nil
}

func (patrolTaskDao *PatrolTaskDao) GetById(id int64) (*PatrolTask, error) {
	var result PatrolTask
	got, err := db.NewSession().ID(id).Get(&result)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().ID(id).Get(&result): %v", err)
	}
	if !got {
		return nil, nil
	}
	return &result, nil
}

func (patrolTaskDao *PatrolTaskDao) CountScheduleByTaskIdSafely(id int64) int64 {
	count, err := db.NewSession().Where("task_id = ?", id).Count(new(TaskSchedule))
	if err != nil {
		return 0
	}
	return count
}

func (patrolTaskDao *PatrolTaskDao) CountPointByTaskIdSafely(id int64) int64 {
	count, err := db.NewSession().Where("task_id = ?", id).Count(new(TaskPointRelation))
	if err != nil {
		return 0
	}
	return count
}

func (patrolTaskDao *PatrolTaskDao) FindRelationsByTaskId(id int64) ([]*TaskPointRelation, error) {
	var relations []*TaskPointRelation
	err := db.NewSession().Where("task_id = ?", id).Find(&relations)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().Where(\"task_id = ?\", id).Find(&relations): %v", err)
	}
	return relations, nil
}

func (patrolTaskDao *PatrolTaskDao) FindSchedulesByTaskId(id int64) ([]*TaskSchedule, error) {
	var schedules []*TaskSchedule
	err := db.NewSession().Where("task_id = ?", id).Find(&schedules)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().Where(\"task_id = ?\", id).Find(&schedules): %v", err)
	}
	return schedules, nil
}

func (patrolTaskDao *PatrolTaskDao) FindAllByEnabled() ([]*EnabledPatrolTaskConfig, error) {
	var tasks []*PatrolTask
	err := db.NewSession().Cols("id").Where("status = ?", true).Find(&tasks)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().Cols(\"id\").Where(\"status = ?\", true).Find(&tasks): %v", err)
	}
	if len(tasks) == 0 {
		return nil, nil
	}
	var tIds []int64
	for _, task := range tasks {
		tIds = append(tIds, task.Id)
	}
	var relations []*TaskPointRelation
	err = db.NewSession().Cols("task_id", "point_id").In("task_id", tIds).Find(&relations)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().Cols(\"task_id\", \"point_id\").In(\"task_id\", tIds).Find(&relations): %v", err)
	}
	relationsMap := make(map[int64][]*TaskPointRelation)
	for _, relation := range relations {
		relationsMap[relation.TaskId] = append(relationsMap[relation.TaskId], relation)
	}
	var schedules []*TaskSchedule
	err = db.NewSession().In("task_id", tIds).Find(&schedules)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().In(\"task_id\", tIds).Find(&schedules): %v", err)
	}
	schedulesMap := make(map[int64][]*TaskSchedule)
	for _, schedule := range schedules {
		schedulesMap[schedule.TaskId] = append(schedulesMap[schedule.TaskId], schedule)
	}
	result := make([]*EnabledPatrolTaskConfig, 0, len(tasks))
	for _, task := range tasks {
		result = append(result, &EnabledPatrolTaskConfig{
			Id:           task.Id,
			Name:         task.Name,
			ScheduleType: task.ScheduleType,
			Relations:    relationsMap[task.Id],
			Schedules:    schedulesMap[task.Id],
		})
	}
	return result, nil
}

func (patrolTaskDao *PatrolTaskDao) Add(pt *PatrolTaskConfig) error {
	return db.DoTransaction(func(session *xorm.Session) error {
		task := &PatrolTask{
			Name:         pt.Name,
			Status:       true,
			ScheduleType: pt.ScheduleType,
			Description:  pt.Description,
		}

		_, err := session.Insert(task)
		if err != nil {
			return fmt.Errorf("session.Insert(pt): %v", err)
		}

		relations := pt.Relations
		for _, relation := range relations {
			relation.TaskId = task.Id
		}
		_, err = session.Insert(relations)
		if err != nil {
			return fmt.Errorf("session.Insert(relations): %v", err)
		}

		schedules := pt.Schedules
		for _, schedule := range schedules {
			schedule.TaskId = task.Id
			schedule.Status = task.Status
		}
		_, err = session.Insert(schedules)
		if err != nil {
			return fmt.Errorf("session.Insert(schedules): %v", err)
		}

		err = ewcpUtil.DoFunction(pt, func(fv interface{}, provider EarlyWarningConfigProvider) error {
			return provider.Save(task.Id, fv)
		}, true)
		if err != nil {
			return err
		}
		return nil
	})
}

func (patrolTaskDao *PatrolTaskDao) Update(pt *PatrolTaskConfig) error {
	return db.DoTransaction(func(session *xorm.Session) error {
		task := &PatrolTask{
			Id:           pt.Id,
			Name:         pt.Name,
			ScheduleType: pt.ScheduleType,
			Description:  pt.Description,
		}

		_, err := session.ID(task.Id).Update(task)
		if err != nil {
			return fmt.Errorf("session.ID(task.Id).Update(pt): %v", err)
		}

		relations := pt.Relations
		_, err = session.Where("task_id = ?", task.Id).Delete(new(TaskPointRelation))
		if err != nil {
			return fmt.Errorf("session.Where(\"task_id = ?\", task.Id).Delete(new(TaskPointRelation)): %v", err)
		}
		for _, relation := range relations {
			relation.TaskId = task.Id
		}
		_, err = session.Insert(relations)
		if err != nil {
			return fmt.Errorf("session.Insert(relations): %v", err)
		}

		schedules := pt.Schedules
		_, err = session.Where("task_id = ?", task.Id).Delete(new(TaskSchedule))
		if err != nil {
			return fmt.Errorf("session.Where(\"task_id = ?\", task.Id).Delete(new(TaskSchedule)): %v", err)
		}
		for _, schedule := range schedules {
			schedule.TaskId = task.Id
			schedule.Status = task.Status
		}
		_, err = session.Insert(schedules)
		if err != nil {
			return fmt.Errorf("session.Insert(schedules): %v", err)
		}

		err = ewcpUtil.DoFunction(pt, func(fv interface{}, provider EarlyWarningConfigProvider) error {
			return provider.Save(task.Id, fv)
		}, true)
		if err != nil {
			return err
		}
		return nil
	})
}

func (patrolTaskDao *PatrolTaskDao) UpdateStatus(id int64, status bool) error {
	return db.DoTransaction(func(session *xorm.Session) error {
		_, err := session.ID(id).Cols("status").Update(&PatrolTask{Status: status})
		if err != nil {
			return fmt.Errorf("session.ID(id).Cols(\"status\").Update(&PatrolTask{Status: status}): %v", err)
		}
		_, err = session.Where("task_id = ?", id).Cols("status").Update(&TaskSchedule{Status: status})
		if err != nil {
			return fmt.Errorf("session.Where(\"task_id = ?\", id).Cols(\"status\").Update(&TaskSchedule{Status: status}): %v", err)
		}
		return nil
	})
}

func (patrolTaskDao *PatrolTaskDao) Delete(id int64) error {
	return db.DoTransaction(func(session *xorm.Session) error {
		_, err := session.ID(id).Delete(new(PatrolTask))
		if err != nil {
			return fmt.Errorf("session.ID(id).Delete(new(PatrolTask)): %v", err)
		}

		_, err = session.Where("task_id = ?", id).Delete(new(TaskPointRelation))
		if err != nil {
			return fmt.Errorf("session.Where(\"task_id = ?\", id).Delete(new(TaskPointRelation)): %v", err)
		}

		_, err = session.Where("task_id = ?", id).Delete(new(TaskSchedule))
		if err != nil {
			return fmt.Errorf("session.Where(\"task_id = ?\", id).Delete(new(TaskSchedule)): %v", err)
		}

		err = ewcpUtil.DoFunction(&PatrolTask{Id: id}, func(fv interface{}, provider EarlyWarningConfigProvider) error {
			return provider.DeleteByTaskId(id)
		}, false)
		if err != nil {
			return err
		}
		return nil
	})
}

func (patrolTaskDao *PatrolTaskDao) initTables() {
	err := db.InitTable(new(PatrolTask), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(PatrolTask), nil): %v", err)
		panic(err)
	}
	err = db.InitTable(new(TaskSchedule), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(TaskSchedule), nil): %v", err)
		panic(err)
	}
	err = db.InitTable(new(TaskPointRelation), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(TaskPointRelation), nil): %v", err)
		panic(err)
	}
}
