package modules

import (
	"fmt"
	"stvpp/internal/algorithm"

	"github.com/iWay7/go-common/db"
	"github.com/iWay7/go-common/logger"
)

type RetrogradeDao struct {
	db.BaseDao
}

type RetrogradeEarlyWarningDao struct {
	db.BaseDao
}

func initRetrogradeDao() {
	tag := "retrograde"
	retrogradeDao := new(RetrogradeDao)
	retrogradeDao.initTables()
	registerAlgorithmProvider(tag, retrogradeDao)

	retrogradeEarlyWarningDao := new(RetrogradeEarlyWarningDao)
	retrogradeEarlyWarningDao.initTables()
	registerEarlyWarningConfigProvider(tag, retrogradeEarlyWarningDao)
}

type Retrograde struct {
	AlgorithmModule `xorm:"extends"`
	ThresholdSec    int          `json:"threshold_sec" xorm:"not null int"`
	MarkPath        *GeomPolygon `json:"mark_path" xorm:"not null"`
	CreatedAt       int64        `json:"created_at" xorm:"index created"`
	UpdatedAt       int64        `json:"updated_at" xorm:"index updated"`
}

type RetrogradeEarlyWarning struct {
	EarlyWarningModule `xorm:"extends"`
	ForceWarning       *ForceWarning `json:"force_warning" xorm:"not null json"`
	CreatedAt          int64         `json:"created_at" xorm:"index created"`
	UpdatedAt          int64         `json:"updated_at" xorm:"index updated"`
}

func (retrograde *Retrograde) TableName() string {
	return "algo_retrograde"
}

func (retrograde *RetrogradeEarlyWarning) TableName() string {
	return "ew_retrograde"
}

func (retrogradeDao *RetrogradeDao) ExistsByArIdAndVIdSafely(arid int64, vid int64) bool {
	count, err := db.NewSession().Where("algorithm_region_id = ? and video_id = ?", arid, vid).Count(new(Retrograde))
	if err != nil {
		return false
	}
	return count > 0
}

func (retrogradeDao *RetrogradeDao) GetByAlgorithmRegionIdSafely(arId int64) interface{} {
	var result Retrograde
	_, err := db.NewSession().Where("algorithm_region_id = ?", arId).Get(&result)
	if err != nil {
		logger.Ef("retrogradeDao.GetByAlgorithmRegionIdSafely: %v", err)
		return nil
	}
	if result.Id == 0 {
		return nil
	}
	return &RetrogradeConfig{
		Id:                  result.Id,
		AlgorithmEnabled:    result.AlgorithmEnabled,
		EarlyWarningEnabled: result.EarlyWarningEnabled,
		ThresholdSec:        result.ThresholdSec,
		MarkPath:            result.MarkPath,
	}
}

func (retrogradeDao *RetrogradeDao) ConfigToModule(cfg interface{}) interface{} {
	if cfg == nil {
		return nil
	}
	c, ok := cfg.(*RetrogradeConfig)
	if !ok {
		return nil
	}
	return &Retrograde{
		AlgorithmModule: AlgorithmModule{
			Id:                  c.Id,
			AlgorithmEnabled:    c.AlgorithmEnabled,
			EarlyWarningEnabled: c.EarlyWarningEnabled,
		},
		ThresholdSec: c.ThresholdSec,
		MarkPath:     c.MarkPath,
	}
}

func (retrogradeDao *RetrogradeDao) Save(ar *Region, i interface{}) error {
	r, ok := i.(*Retrograde)
	if !ok {
		return fmt.Errorf("retrogradeDao.Save: invalid type %T", r)
	}
	r.VideoId = ar.VideoId
	r.AlgorithmRegionId = ar.Id
	if r.Id != 0 {
		_, err := db.GetWithTransactionOrNewSession().ID(r.Id).AllCols().Update(r)
		if err != nil {
			return fmt.Errorf("db.NewSession().ID(r.AlgorithmConfigId).AllCols().Update(r): %v", err)
		}
		return nil
	}
	_, err := db.GetWithTransactionOrNewSession().Insert(r)
	if err != nil {
		return fmt.Errorf("db.GetWithTransactionOrNewSession().Insert(r): %v", err)
	}
	return nil
}

func (retrogradeDao *RetrogradeDao) GetConfigForZone(zone *algorithm.TrackZone, ar *Region) error {
	cfg := retrogradeDao.GetByAlgorithmRegionIdSafely(ar.Id)
	if cfg != nil {
		type Cfg struct {
			ThresholdSec int          `json:"threshold_sec"`
			MarkPath     *GeomPolygon `json:"mark_path"`
		}
		c := cfg.(*RetrogradeConfig)
		zone.Retrograde = &Cfg{
			ThresholdSec: c.ThresholdSec,
			MarkPath:     c.MarkPath,
		}
	}
	return nil
}

func (retrogradeDao *RetrogradeDao) DeleteByAlgorithmRegionId(id int64) error {
	_, err := db.GetWithTransactionOrNewSession().Where("algorithm_region_id = ?", id).Delete(new(Retrograde))
	if err != nil {
		return fmt.Errorf("db.GetWithTransactionOrNewSession().Where(\"algorithm_region_id = ?\", id).Delete(new(Retrograde)): %v", err)
	}
	return nil
}

func (retrogradeDao *RetrogradeDao) initTables() {
	err := db.InitTable(new(Retrograde), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(Retrograde), nil): %v", err)
		panic(err)
	}
}

func (retrogradeEarlyWarningDao *RetrogradeEarlyWarningDao) Save(taskId int64, i interface{}) error {
	ew, ok := i.(*RetrogradeEarlyWarning)
	if !ok {
		return fmt.Errorf("retrogradeEarlyWarningDao.Save: invalid type %T", i)
	}
	ew.TaskId = taskId
	if ew.Id != 0 {
		_, err := db.GetWithTransactionOrNewSession().ID(ew.Id).AllCols().Update(ew)
		if err != nil {
			return fmt.Errorf("db.NewSession().ID(ew.Id).AllCols().Update(ew): %v", err)
		}
		return nil
	}
	_, err := db.GetWithTransactionOrNewSession().Insert(ew)
	if err != nil {
		return fmt.Errorf("db.NewSession().Insert(ew): %v", err)
	}
	return nil
}

func (retrogradeEarlyWarningDao *RetrogradeEarlyWarningDao) DeleteByTaskId(taskId int64) error {
	_, err := db.GetWithTransactionOrNewSession().Where("task_id = ?", taskId).Delete(new(RetrogradeEarlyWarning))
	if err != nil {
		return fmt.Errorf("db.NewSession().Where(\"task_id = ?\", taskId).Delete(new(RetrogradeEarlyWarning)): %v", err)
	}
	return nil
}

func (retrogradeEarlyWarningDao *RetrogradeEarlyWarningDao) initTables() {
	err := db.InitTable(new(RetrogradeEarlyWarning), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(RetrogradeEarlyWarning), nil): %v", err)
		panic(err)
	}
}
