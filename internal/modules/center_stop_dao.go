package modules

import (
	"fmt"
	"stvpp/internal/algorithm"

	"github.com/iWay7/go-common/db"
	"github.com/iWay7/go-common/logger"
)

type CenterStopDao struct {
	db.BaseDao
}

type CenterStopEarlyWarningDao struct {
	db.BaseDao
}

func initCenterStopDao() {
	tag := "center_stop"
	centerStopDao := new(CenterStopDao)
	centerStopDao.initTables()
	registerAlgorithmProvider(tag, centerStopDao)

	centerStopEarlyWarningDao := new(CenterStopEarlyWarningDao)
	centerStopEarlyWarningDao.initTables()
	registerEarlyWarningConfigProvider(tag, centerStopEarlyWarningDao)
}

type CenterStop struct {
	AlgorithmModule      `xorm:"extends"`
	MarkType             int   `json:"mark_type" xorm:"not null int"`
	MaxStopSec           int   `json:"max_stop_sec" xorm:"not null int"`
	AllowDoubleFlash     bool  `json:"allow_double_flash" xorm:"not null bool"`
	AllowWarningTriangle bool  `json:"allow_warning_triangle" xorm:"not null bool"`
	CreatedAt            int64 `json:"created_at" xorm:"index created"`
	UpdatedAt            int64 `json:"updated_at" xorm:"index updated"`
}

type CenterStopEarlyWarning struct {
	EarlyWarningModule `xorm:"extends"`
	HistoryDeviation   int           `json:"history_deviation" xorm:"not null int"`
	ForceWarning       *ForceWarning `json:"force_warning" xorm:"not null json"`
	CreatedAt          int64         `json:"created_at" xorm:"index created"`
	UpdatedAt          int64         `json:"updated_at" xorm:"index updated"`
}

func (s *CenterStop) TableName() string {
	return "algo_center_stop"
}

func (ew *CenterStopEarlyWarning) TableName() string {
	return "ew_center_stop"
}

func (dao *CenterStopDao) ExistsByArIdAndVIdSafely(arid int64, vid int64) bool {
	count, err := db.NewSession().Where("algorithm_region_id = ? and video_id = ?", arid, vid).Count(new(CenterStop))
	if err != nil {
		return false
	}
	return count > 0
}

func (dao *CenterStopDao) GetByAlgorithmRegionIdSafely(arId int64) interface{} {
	var result CenterStop
	_, err := db.NewSession().Where("algorithm_region_id = ?", arId).Get(&result)
	if err != nil {
		logger.Ef("dao.GetByAlgorithmRegionIdSafely: %v", err)
		return nil
	}
	if result.Id == 0 {
		return nil
	}
	return &CenterStopConfig{
		Id:                   result.Id,
		AlgorithmEnabled:     result.AlgorithmEnabled,
		EarlyWarningEnabled:  result.EarlyWarningEnabled,
		MaxStopSec:           result.MaxStopSec,
		AllowDoubleFlash:     result.AllowDoubleFlash,
		AllowWarningTriangle: result.AllowWarningTriangle,
	}
}

func (dao *CenterStopDao) ConfigToModule(cfg interface{}) interface{} {
	if cfg == nil {
		return nil
	}
	c, ok := cfg.(*CenterStopConfig)
	if !ok {
		return nil
	}
	return &CenterStop{
		AlgorithmModule: AlgorithmModule{
			Id:                  c.Id,
			AlgorithmEnabled:    c.AlgorithmEnabled,
			EarlyWarningEnabled: c.EarlyWarningEnabled,
		},
		MaxStopSec:           c.MaxStopSec,
		AllowDoubleFlash:     c.AllowDoubleFlash,
		AllowWarningTriangle: c.AllowWarningTriangle,
	}
}

func (dao *CenterStopDao) Save(ar *Region, i interface{}) error {
	cs, ok := i.(*CenterStop)
	if !ok {
		return fmt.Errorf("dao.Save: invalid type %T", i)
	}
	cs.VideoId = ar.VideoId
	cs.AlgorithmRegionId = ar.Id
	if cs.Id != 0 {
		_, err := db.GetWithTransactionOrNewSession().ID(cs.Id).AllCols().Update(cs)
		if err != nil {
			return fmt.Errorf("db.GetWithTransactionOrNewSession().ID(cs.AlgorithmConfigId).AllCols().Update(cs): %v", err)
		}
		return nil
	}
	_, err := db.GetWithTransactionOrNewSession().Insert(cs)
	if err != nil {
		return fmt.Errorf("db.GetWithTransactionOrNewSession().Insert(cs): %v", err)
	}
	return nil
}

func (dao *CenterStopDao) GetConfigForZone(zone *algorithm.TrackZone, ar *Region) error {
	cfg := dao.GetByAlgorithmRegionIdSafely(ar.Id)
	if cfg != nil {
		type Cfg struct {
			MaxStopSec           int  `json:"max_stop_sec"`
			AllowDoubleFlash     bool `json:"allow_double_flash"`
			AllowWarningTriangle bool `json:"allow_warning_triangle"`
		}
		c := cfg.(*CenterStopConfig)
		zone.CenterStop = &Cfg{
			MaxStopSec:           c.MaxStopSec,
			AllowDoubleFlash:     c.AllowDoubleFlash,
			AllowWarningTriangle: c.AllowWarningTriangle,
		}
	}
	return nil
}

func (dao *CenterStopDao) DeleteByAlgorithmRegionId(id int64) error {
	_, err := db.GetWithTransactionOrNewSession().Where("algorithm_region_id = ?", id).Delete(new(CenterStop))
	if err != nil {
		return fmt.Errorf("db.GetWithTransactionOrNewSession().Where(\"algorithm_region_id = ?\", id).Delete(new(CenterStop)): %v", err)
	}
	return nil
}

func (dao *CenterStopDao) initTables() {
	err := db.InitTable(new(CenterStop), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(CenterStop), nil): %v", err)
		panic(err)
	}
}

func (dao *CenterStopEarlyWarningDao) Save(taskId int64, i interface{}) error {
	ew, ok := i.(*CenterStopEarlyWarning)
	if !ok {
		return fmt.Errorf("dao.Save: invalid type %T", i)
	}
	ew.TaskId = taskId
	if ew.Id != 0 {
		_, err := db.GetWithTransactionOrNewSession().ID(ew.Id).AllCols().Update(ew)
		if err != nil {
			return fmt.Errorf("db.NewSession().ID(ew.Id).AllCols().Update(ew): %v", err)
		}
		return nil
	}
	_, err := db.GetWithTransactionOrNewSession().Insert(ew)
	if err != nil {
		return fmt.Errorf("db.NewSession().Insert(ew): %v", err)
	}
	return nil
}

func (dao *CenterStopEarlyWarningDao) DeleteByTaskId(id int64) error {
	_, err := db.GetWithTransactionOrNewSession().Where("task_id = ?", id).Delete(new(CenterStopEarlyWarning))
	if err != nil {
		return fmt.Errorf("db.NewSession().Where(\"task_id = ?\", id).Delete(new(CenterStopEarlyWarning)): %v", err)
	}
	return nil
}

func (dao *CenterStopEarlyWarningDao) initTables() {
	err := db.InitTable(new(CenterStopEarlyWarning), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(CenterStopEarlyWarning), nil): %v", err)
		panic(err)
	}
}
