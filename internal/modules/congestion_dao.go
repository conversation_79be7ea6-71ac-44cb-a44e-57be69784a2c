package modules

import (
	"fmt"
	"stvpp/internal/algorithm"

	"github.com/iWay7/go-common/db"
	"github.com/iWay7/go-common/logger"
)

type CongestionAlgorithmDao struct {
	db.BaseDao
}

type CongestionEarlyWarningDao struct {
	db.BaseDao
}

func initCongestionDao() {
	tag := "congestion"
	congestionAlgorithmDao := new(CongestionAlgorithmDao)
	congestionAlgorithmDao.initTables()
	registerAlgorithmProvider(tag, congestionAlgorithmDao)

	congestionEarlyWarningDao := new(CongestionEarlyWarningDao)
	congestionEarlyWarningDao.initTables()
	registerEarlyWarningConfigProvider(tag, congestionEarlyWarningDao)
}

type CongestionAlgorithm struct {
	AlgorithmModule `xorm:"extends"`
	MaxPendingSec   int   `json:"max_pending_sec" xorm:"not null int"`
	CreatedAt       int64 `json:"created_at" xorm:"index created"`
	UpdatedAt       int64 `json:"updated_at" xorm:"index updated"`
}

type CongestionEarlyWarning struct {
	EarlyWarningModule `xorm:"extends"`
	HistoryDeviation   int   `json:"history_deviation" xorm:"not null int"`
	CreatedAt          int64 `json:"created_at" xorm:"index created"`
	UpdatedAt          int64 `json:"updated_at" xorm:"index updated"`
}

func (congestion *CongestionAlgorithm) TableName() string {
	return "algo_congestion"
}

func (earlyWarning *CongestionEarlyWarning) TableName() string {
	return "ew_congestion"
}

func (dao *CongestionAlgorithmDao) ExistsByArIdAndVIdSafely(arid int64, vid int64) bool {
	count, err := db.NewSession().Where("algorithm_region_id = ? and video_id = ?", arid, vid).Count(new(CongestionAlgorithm))
	if err != nil {
		return false
	}
	return count > 0
}

func (dao *CongestionAlgorithmDao) GetByAlgorithmRegionIdSafely(arId int64) interface{} {
	var result CongestionAlgorithm
	_, err := db.NewSession().Where("algorithm_region_id = ?", arId).Get(&result)
	if err != nil {
		logger.Ef("dao.GetByAlgorithmRegionIdSafely: %v", err)
		return nil
	}
	if result.Id == 0 {
		return nil
	}
	return &CongestionConfig{
		Id:                  result.Id,
		AlgorithmEnabled:    result.AlgorithmEnabled,
		EarlyWarningEnabled: result.EarlyWarningEnabled,
		MaxPendingSec:       result.MaxPendingSec,
	}
}

func (dao *CongestionAlgorithmDao) ConfigToModule(cfg interface{}) interface{} {
	if cfg == nil {
		return nil
	}
	c, ok := cfg.(*CongestionConfig)
	if !ok {
		return nil
	}
	return &CongestionAlgorithm{
		AlgorithmModule: AlgorithmModule{
			Id:                  c.Id,
			AlgorithmEnabled:    c.AlgorithmEnabled,
			EarlyWarningEnabled: c.EarlyWarningEnabled,
		},
		MaxPendingSec: c.MaxPendingSec,
	}
}

func (dao *CongestionAlgorithmDao) Save(ar *Region, i interface{}) error {
	c, ok := i.(*CongestionAlgorithm)
	if !ok {
		return fmt.Errorf("dao.Save: invalid type %T", c)
	}
	c.VideoId = ar.VideoId
	c.AlgorithmRegionId = ar.Id
	if c.Id != 0 {
		_, err := db.GetWithTransactionOrNewSession().ID(c.Id).AllCols().Update(c)
		if err != nil {
			return fmt.Errorf("db.NewSession().ID(c.AlgorithmConfigId).AllCols().Update(c): %v", err)
		}
		return nil
	}
	_, err := db.GetWithTransactionOrNewSession().Insert(c)
	if err != nil {
		return fmt.Errorf("db.NewSession().Insert(c): %v", err)
	}
	return nil
}

func (dao *CongestionAlgorithmDao) GetConfigForZone(zone *algorithm.TrackZone, ar *Region) error {
	cfg := dao.GetByAlgorithmRegionIdSafely(ar.Id)
	if cfg != nil {
		type Cfg struct {
			MaxPendingSec int `json:"max_pending_sec"`
		}
		c := cfg.(*CongestionConfig)
		zone.Congestion = &Cfg{
			MaxPendingSec: c.MaxPendingSec,
		}
	}
	return nil
}

func (dao *CongestionAlgorithmDao) DeleteByAlgorithmRegionId(id int64) error {
	_, err := db.GetWithTransactionOrNewSession().Where("algorithm_region_id = ?", id).Delete(new(CongestionAlgorithm))
	if err != nil {
		return fmt.Errorf("db.NewSession().Where(\"algorithm_region_id = ?\", id).Delete(new(CongestionAlgorithm)): %v", err)
	}
	return nil
}

func (dao *CongestionAlgorithmDao) initTables() {
	err := db.InitTable(new(CongestionAlgorithm), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(CongestionAlgorithm), nil): %v", err)
		panic(err)
	}
	err = db.InitTable(new(CongestionEarlyWarning), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(CongestionEarlyWarning), nil): %v", err)
		panic(err)
	}
}

func (dao *CongestionEarlyWarningDao) Save(taskId int64, i interface{}) error {
	ew, ok := i.(*CongestionEarlyWarning)
	if !ok {
		return fmt.Errorf("dao.Save: invalid type %T", i)
	}
	ew.TaskId = taskId
	if ew.Id != 0 {
		_, err := db.GetWithTransactionOrNewSession().ID(ew.Id).AllCols().Update(ew)
		if err != nil {
			return fmt.Errorf("db.NewSession().ID(ew.Id).AllCols().Update(ew): %v", err)
		}
		return nil
	}
	_, err := db.GetWithTransactionOrNewSession().Insert(ew)
	if err != nil {
		return fmt.Errorf("db.NewSession().Insert(ew): %v", err)
	}
	return nil
}

func (dao *CongestionEarlyWarningDao) DeleteByTaskId(id int64) error {
	_, err := db.GetWithTransactionOrNewSession().Where("task_id = ?", id).Delete(new(CongestionEarlyWarning))
	if err != nil {
		return fmt.Errorf("db.NewSession().Where(\"task_id = ?\", id).Delete(new(CongestionEarlyWarning)): %v", err)
	}
	return nil
}

func (dao *CongestionEarlyWarningDao) initTables() {
	err := db.InitTable(new(CongestionEarlyWarning), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(CongestionEarlyWarning), nil): %v", err)
		panic(err)
	}
}
