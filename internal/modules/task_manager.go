package modules

import (
	"fmt"
	"strconv"
	"stvpp/global"
	"stvpp/internal/algorithm"
	"stvpp/internal/common"
	"sync"

	"github.com/iWay7/go-common/logger"
	"github.com/iWay7/go-common/misc"
)

var (
	taskManager    *TaskManager
	taskLogger     *logger.MiniLogger
	trackerManager *algorithm.TrackerManager
	nodeManager    *algorithm.NodeManager
)

func init() {
	taskLogger = logger.NewFileLogger(global.TaskLogFile, 10*1024*1024)
}

type TaskManager struct {
	mu    sync.RWMutex
	tasks map[int64]*taskConfig
}

type taskConfig struct {
	TaskId       int64                         `json:"task_id"`
	TaskName     string                        `json:"task_name"`
	ScheduleType int                           `json:"schedule_type"`
	AlgoSchedule map[common.AlgoType][]*Period `json:"algo_schedule"`
	Videos       []*taskVideoConfig            `json:"videos"`
}

type taskVideoConfig struct {
	VideoId   int64             `json:"video_id"`
	CID       string            `json:"cid"`
	Ip        string            `json:"ip"`
	Port      int               `json:"port"`
	Rtsp      string            `json:"rtsp"`
	AlgoTypes []common.AlgoType `json:"algo_types"`
}

func InitTaskManager() {
	trackerManager = algorithm.GetTrackerManager()
	nodeManager = algorithm.GetNodeManager()

	taskManager = &TaskManager{
		tasks: make(map[int64]*taskConfig),
	}
	taskManager.initializeAllTasks()

	trackerManager.SetStatUpdateHandler(func(vt *algorithm.VideoTracker) {
		video, err := videoDao.GetByGbCode(vt.CID)
		if err != nil {
			logger.Ef("根据gb_code %s 获取视频失败: %v", vt.CID, err)
			return
		}
		if video == nil {
			logger.Ef("gb_code为 %s 的视频未找到", vt.CID)
			return
		}
		err = videoTrackerStatDao.Create(&VideoTrackerStat{
			VideoId: video.Id,
			NodeId:  vt.NodeID,
			Status:  int(vt.GetStatus()),
			ImgUrl:  vt.ImgUrl,
		})
		if err != nil {
			logger.Ef("为视频 %d 创建追踪器状态失败: %v", video.Id, err)
		}
	})

	misc.RegisterEventReceiver(func(event string, data interface{}) {
		if event == algorithm.EventNodeInit {
			taskLogger.Append("I: 检测到新算法节点上线，开始重新加载所有任务...")
			go taskManager.checkAndRunTasks()
		}
	})

	// misc.ScheduleTask(misc.Now().Add(time.Minute).Unix(), func() int64 {
	// 	go taskManager.checkAndRunTasks()
	// 	return misc.Now().Add(time.Minute).Unix()
	// })
}

func GetTaskManager() *TaskManager {
	return taskManager
}

func (tm *TaskManager) initializeAllTasks() {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	taskConfigs, err := GetPatrolTaskDao().FindAllByEnabled()
	if err != nil {
		taskLogger.Appendf("E: 查询所有启用的任务失败: %v", err)
		return
	}
	if len(taskConfigs) == 0 {
		taskLogger.Append("W: 未找到启用的任务。")
		return
	}

	taskIds := make([]int64, 0, len(tm.tasks))
	taskScheduleMap := make(map[int64][]*TaskSchedule)
	taskPointMap := make(map[int64][]int64)
	allPointIds := make([]int64, 0, len(taskConfigs))
	for _, tc := range taskConfigs {
		taskIds = append(taskIds, tc.Id)
		taskScheduleMap[tc.Id] = tc.Schedules
		for _, relation := range tc.Relations {
			allPointIds = append(allPointIds, relation.PointId)
		}
		taskPointMap[tc.Id] = allPointIds
	}

	if len(allPointIds) == 0 {
		taskLogger.Append("W: 未找到任务点位ID")
		return
	}
	videoIdMap, err := GetPointDao().FindVideoIdsInPointIds(allPointIds)
	if err != nil {
		taskLogger.Appendf("E: 查询点位ID %v 的视频ID失败: %v", allPointIds, err)
		return
	}

	for _, task := range taskConfigs {
		taskId := task.Id
		schedules := taskScheduleMap[taskId]
		pointIds := taskPointMap[taskId]
		algoSchedule := make(map[common.AlgoType][]*Period, len(schedules))
		algoTypes := make([]common.AlgoType, 0, len(schedules))
		for _, schedule := range schedules {
			algoSchedule[schedule.AlgoType] = schedule.Periods
			algoTypes = append(algoTypes, schedule.AlgoType)
		}
		videos := make([]*taskVideoConfig, 0, len(pointIds))
		for _, pointId := range pointIds {
			videoIds := videoIdMap[pointId]
			for _, vid := range videoIds {
				var video *Video
				video, err = videoDao.GetById(vid)
				if err != nil {
					taskLogger.Appendf("E: 查询视频失败：ID %d: %v", vid, err)
					continue
				}
				if video == nil {
					taskLogger.Appendf("W: 未找到此视频：ID %d", vid)
					continue
				}
				videos = append(videos, &taskVideoConfig{
					VideoId:   vid,
					CID:       video.GbCode,
					Ip:        video.Ip,
					Port:      video.Port,
					Rtsp:      video.Rtsp,
					AlgoTypes: algoTypes,
				})
			}
		}
		tm.tasks[taskId] = &taskConfig{
			TaskId:       taskId,
			TaskName:     task.Name,
			ScheduleType: task.ScheduleType,
			AlgoSchedule: algoSchedule,
			Videos:       videos,
		}
	}
}

func (tm *TaskManager) checkAndRunTasks() {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if len(nodeManager.GetAliveNodes()) == 0 {
		taskLogger.Append("W: 初始化中止: 未发现可用的算法节点。")
		return
	}

	videoMap := make(map[int64]*taskVideoConfig)
	for _, task := range tm.tasks {
		if !tm.isScheduleValid(task) {
			taskLogger.Appendf("W: 任务 %s 不在执行周期内，跳过。", task.TaskName)
			continue
		}
		if len(task.Videos) == 0 {
			taskLogger.Appendf("W: 任务 %s 未找到视频，跳过。", task.TaskName)
			continue
		}
		for _, video := range task.Videos {
			vc, ok := videoMap[video.VideoId]
			if !ok {
				vc = video
			}
			vc.AlgoTypes = mergeUniqueAlgoType(vc.AlgoTypes, video.AlgoTypes)
			videoMap[video.VideoId] = vc
		}
	}
	if len(videoMap) == 0 {
		taskLogger.Append("W: 未找到任何视频，任务取消执行。")
		return
	}

	for _, vc := range videoMap {
		go func(vc *taskVideoConfig) {
			err := tm.runTaskForVideo(vc)
			if err != nil {
				taskLogger.Appendf("E: 启动视频追踪器 [%s] 失败: %v", vc.CID, err)
			}
		}(vc)
	}
}

func (tm *TaskManager) isScheduleValid(task *taskConfig) bool {
	now := misc.Now()
	for _, periods := range task.AlgoSchedule {
		for _, period := range periods {
			if period.IsInRange(now) {
				return true
			}
		}
	}
	return true
}

func (tm *TaskManager) runTaskForVideo(vc *taskVideoConfig) error {
	cid := vc.CID
	if err := trackerManager.DeleteTrackerAnyway(cid); err != nil {
		logger.Ef("移除CID %s 的追踪器失败: %v", cid, err)
		return err
	}

	// 添加追踪器
	if err := trackerManager.AddTracker(algorithm.MsgTrackAdd{
		CID:           cid,
		PlatIp:        vc.Ip,
		PlatPort:      strconv.Itoa(vc.Port),
		LocalFilePath: vc.Rtsp,
	}); err != nil {
		logger.E(err.Error())
		return err
	}

	// 为该视频配置并启动追踪器
	algorithmRegions, err := regionDao.FindByVideoId(vc.VideoId)
	if err != nil {
		logger.Ef("查找视频ID %d 的算法区域失败: %v", cid, err)
		return err
	}

	var zones []algorithm.TrackZone
	for _, ar := range algorithmRegions {
		zone := algorithm.TrackZone{
			ZID:           fmt.Sprintf("AR_%d", ar.Id),
			Direction:     ar.Direction,
			XYXY:          convertFloat64To32(ar.MarkRegion.FlatCoords()),
			LaneDirection: ar.LaneDirection,
			LaneType:      ar.LaneType,
			LaneNumber:    ar.LaneNumber,
		}
		err = acpUtil.DoFunction(&zone, func(fv interface{}, provider AlgorithmConfigProvider) error {
			err = provider.GetConfigForZone(&zone, ar)
			if err != nil {
				logger.Ef("从提供者获取区域 %d 的配置时出错: %v", ar.Id, err)
				return err
			}
			return nil
		}, false)
		if err != nil {
			logger.Ef("为区域 %d 获取配置时出错: %v", ar.Id, err)
			return err
		}

		if zone.HasValidConfig() {
			zones = append(zones, zone)
		}
	}

	if len(zones) == 0 {
		logger.If("视频ID %d 的算法区域没有有效的算法配置，跳过配置和启动。", cid)
		return err
	}

	cfg := algorithm.MsgTrackCfg{
		CID:       cid,
		Zones:     zones,
		ImageSize: []int{1920, 1080},
	}
	for i := range cfg.Zones {
		cfg.Zones[i].ZID = fmt.Sprintf("%s_%s", cid, cfg.Zones[i].ZID)
	}

	if err = trackerManager.ConfigureTracker(cfg); err != nil {
		logger.E(err.Error())
		return err
	}

	if err = trackerManager.StartTracker(cid); err != nil {
		return err
	}
	return nil
}

func (tm *TaskManager) ReloadTask(taskId int64) {
	taskLogger.Appendf("I: 正在重新加载任务 ID: %d...", taskId)
	go tm.initializeAllTasks()
	taskLogger.Appendf("I: 任务 %d 的重载请求已触发。", taskId)
}

func (tm *TaskManager) RemoveTask(taskId int64) {
	taskLogger.Appendf("I: 正在移除任务 ID: %d...", taskId)
	go tm.initializeAllTasks()
	taskLogger.Appendf("I: 任务 %d 的移除请求已触发。", taskId)
}

func mergeUniqueAlgoType(slice1, slice2 []common.AlgoType) []common.AlgoType {
	// 使用 map 记录已出现的元素
	seen := make(map[common.AlgoType]bool)
	var result []common.AlgoType

	// 处理第一个切片
	for _, v := range slice1 {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}

	// 处理第二个切片
	for _, v := range slice2 {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}

	return result
}

func convertFloat64To32(in []float64) []float32 {
	out := make([]float32, len(in))
	for i, v := range in {
		out[i] = float32(v)
	}
	return out
}
