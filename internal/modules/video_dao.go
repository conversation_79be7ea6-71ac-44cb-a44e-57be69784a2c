package modules

import (
	"fmt"
	"strconv"

	"github.com/iWay7/go-common/db"
)

type VideoDao struct {
	db.BaseDao
}

var videoDao *VideoDao

func initVideoDao() {
	videoDao = new(VideoDao)
	videoDao.initTables()
}

func GetVideoDao() *VideoDao {
	return videoDao
}

type Video struct {
	Id             int64   `json:"id" xorm:"pk autoincr bigint"`
	PlatformId     int64   `json:"platform_id" xorm:"not null bigint"`
	OrganizeNumber string  `json:"organize_number" xorm:"not null varchar(255)"`
	Name           string  `json:"name" xorm:"not null varchar(255)"`
	GbCode         string  `json:"gb_code" xorm:"varchar(255)"`
	Manufacturer   string  `json:"manufacturer" xorm:"not null char(12)"`
	Type           string  `json:"type" xorm:"char(1)"`
	Ip             string  `json:"ip" xorm:"not null varchar(255)"`
	Port           int     `json:"port" xorm:"not null int"`
	Username       string  `json:"username" xorm:"not null varchar(255)"`
	Password       string  `json:"password" xorm:"not null varchar(255)"`
	Rtsp           string  `json:"rtsp" xorm:"varchar(255)"`
	Longitude      float64 `json:"longitude" xorm:"double"`
	Latitude       float64 `json:"latitude" xorm:"double"`
	Description    string  `json:"description" xorm:"varchar(255)"`
	Status         int     `json:"status" xorm:"not null default 0"`
	CreatedAt      int64   `json:"created_at" xorm:"index created"`
	UpdatedAt      int64   `json:"updated_at" xorm:"index updated"`
	Sbxh           int64   `json:"-" xorm:"not null bigint"`
	Tdxh           int64   `json:"-" xorm:"unique not null bigint"`
}

func (video *Video) TableName() string {
	return "base_video"
}

func (videoDao *VideoDao) FindByFilter(name, gbCode string, status int, limit, start int) ([]*Video, int64, error) {
	var result []*Video
	where := "1 = 1"
	if name != "" {
		where += "and name like '%" + name + "%'"
	}
	if gbCode != "" {
		where += " and gb_code like '%" + gbCode + "%'"
	}
	if status != 0 {
		where += " and status = " + strconv.Itoa(status)
	}
	count, err := db.NewSession().Where(where).Limit(limit, start).FindAndCount(&result)
	if err != nil {
		return nil, 0, fmt.Errorf("session.Where(where).Limit(limit, start).FindAndCount(&result): %v", err)
	}
	return result, count, err
}

func (videoDao *VideoDao) FindInIds(ids []int64) ([]*Video, error) {
	var result []*Video
	err := db.NewSession().In("id", ids).Find(&result)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().In(\"id\", ids).Find(&result): %v", err)
	}
	return result, nil
}

func (videoDao *VideoDao) GetById(id int64) (*Video, error) {
	var result Video
	has, err := db.NewSession().ID(id).Get(&result)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().ID(id).Get(&result): %v", err)
	}
	if !has {
		return nil, nil
	}
	return &result, nil
}

func (videoDao *VideoDao) GetByGbCode(gbCode string) (*Video, error) {
	var result Video
	has, err := db.NewSession().Where("gb_code = ?", gbCode).Get(&result)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().Where(\"gb_code = ?\", gbCode).Get(&result): %v", err)
	}
	if !has {
		return nil, nil
	}
	return &result, nil
}

func (videoDao *VideoDao) Add(video *Video) error {
	_, err := db.NewSession().Insert(video)
	if err != nil {
		return fmt.Errorf("db.NewSession().Insert(video): %v", err)
	}
	return nil
}

func (videoDao *VideoDao) Update(video *Video) error {
	_, err := db.NewSession().ID(video.Id).AllCols().Update(video)
	if err != nil {
		return fmt.Errorf("db.NewSession().ID(video.AlgorithmConfigId).AllCols().Update(video): %v", err)
	}
	return nil
}

func (videoDao *VideoDao) UpdateByTdxh(video *Video) error {
	_, err := db.NewSession().Where("tdxh = ?", video.Tdxh).AllCols().Update(video)
	if err != nil {
		return fmt.Errorf("db.NewSession().Where(\"tdxh = ?\", video.Tdxh).AllCols().Update(video): %v", err)
	}
	return nil
}

func (videoDao *VideoDao) ExistsByTdxh(tdxh int64) (bool, error) {
	exist, err := db.NewSession().Where("tdxh = ?", tdxh).Exist(new(Video))
	if err != nil {
		return false, fmt.Errorf("db.NewSession().Where(\"tdxh = ?\", tdxh).Count(new(Video)): %v", err)
	}
	return exist, nil
}

func (videoDao *VideoDao) Save(video *Video) error {
	exist, err := videoDao.ExistsByTdxh(video.Tdxh)
	if err != nil {
		return err
	}
	if exist {
		return videoDao.UpdateByTdxh(video)
	} else {
		return videoDao.Add(video)
	}
}

func (videoDao *VideoDao) FindByBoundary(boundary *GeomPolygon, k string, limit, start int) ([]*Video, int64, error) {
	var result []*Video
	where := ""
	if boundary != nil {
		str, err := boundary.ToDbGeometryString()
		if err != nil {
			return nil, 0, fmt.Errorf("invalid boundary: %v", err)
		}
		where = "ST_Intersects(" + str + ", ST_PointFromText(CONCAT('POINT(', latitude, ' ', longitude, ')'), 4326))"
	}
	if k != "" {
		where += " and (name like '%" + k + "%' or gb_code like '%" + k + "%')"
	}
	count, err := db.NewSession().Where(where).Limit(limit, start).FindAndCount(&result)
	if err != nil {
		return nil, 0, fmt.Errorf("db.Db().Where(\"st_intersects(boundary, ?)\", boundary).Find(&result): %v", err)
	}
	return result, count, nil
}

func (videoDao *VideoDao) initTables() {
	err := db.InitTable(new(Video), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(Video), nil): %v", err)
		panic(err)
	}
}
