package modules

import (
	"encoding/json"
	"fmt"

	"github.com/twpayne/go-geom"
	"github.com/twpayne/go-geom/encoding/wkt"
)

type GeomPolygon struct {
	*geom.Polygon
}

type GeomPoint struct {
	*geom.Point
}

func (p *GeomPolygon) ToDbGeometryString() (string, error) {
	str, err := p.ToGeometryString()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("ST_GeomFromText('%s', 4326)", str), nil
}

func (p *GeomPolygon) ToGeometryString() (string, error) {
	if p == nil || p.Polygon == nil {
		return "", fmt.Errorf("polygon is nil")
	}
	wktString, err := wkt.Marshal(p.Polygon.SetSRID(4326))
	if err != nil {
		return "", err
	}
	return wktString, nil
}

func (p *GeomPolygon) MarshalJSON() ([]byte, error) {
	if p == nil || p.Polygon == nil {
		return json.Marshal(nil)
	}
	return json.Marshal(p.Polygon.Coords()[0])
}

func (p *GeomPolygon) UnmarshalJSON(data []byte) error {
	if len(data) == 0 {
		p.Polygon = nil
		return nil
	}
	var coords []geom.Coord
	if err := json.Unmarshal(data, &coords); err != nil {
		return err
	}
	if len(coords) == 0 {
		p.Polygon = nil
		return nil
	}
	poly, err := geom.NewPolygon(geom.XY).SetCoords([][]geom.Coord{coords})
	if err != nil {
		return err
	}
	p.Polygon = poly
	return nil
}

func (p *GeomPolygon) FromDB(bytes []byte) error {
	if bytes == nil {
		p.Polygon = nil
		return nil
	}
	return p.UnmarshalJSON(bytes)
}

func (p *GeomPolygon) ToDB() ([]byte, error) {
	if p == nil || p.Polygon == nil {
		return nil, nil
	}
	return p.MarshalJSON()
}

func (p *GeomPoint) ToDbGeometryString() (string, error) {
	str, err := p.ToGeometryString()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("ST_GeomFromText('%s', 4326)", str), nil
}

func (p *GeomPoint) ToGeometryString() (string, error) {
	if p == nil || p.Point == nil {
		return "", fmt.Errorf("point is nil")
	}
	wktString, err := wkt.Marshal(p.Point.SetSRID(4326))
	if err != nil {
		return "", err
	}
	return wktString, nil
}

func (p *GeomPoint) MarshalJSON() ([]byte, error) {
	if p == nil || p.Point == nil {
		return json.Marshal(nil)
	}
	return json.Marshal(p.Point.Coords())
}

func (p *GeomPoint) UnmarshalJSON(data []byte) error {
	if len(data) == 0 {
		p.Point = nil
		return nil
	}
	var coords geom.Coord
	if err := json.Unmarshal(data, &coords); err != nil {
		return err
	}
	point, err := geom.NewPoint(geom.XY).SetCoords(coords)
	if err != nil {
		return fmt.Errorf("failed to create point: %v", err)
	}
	p.Point = point
	return nil
}

func (p *GeomPoint) FromDB(bytes []byte) error {
	if bytes == nil {
		p.Point = nil
		return nil
	}
	return p.UnmarshalJSON(bytes)
}

func (p *GeomPoint) ToDB() ([]byte, error) {
	if p == nil || p.Point == nil {
		return nil, nil
	}
	return p.MarshalJSON()
}
