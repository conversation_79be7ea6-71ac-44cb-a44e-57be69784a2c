package modules

import (
	"stvpp/internal/algorithm"
	"stvpp/internal/common"
)

var acpUtil *AlgorithmConfigProviderUtil

func init() {
	acpUtil = &AlgorithmConfigProviderUtil{
		ProviderUtil: common.NewProviderUtil[AlgorithmConfigProvider](),
	}
}

func GetACPUtil() *AlgorithmConfigProviderUtil {
	return acpUtil
}

type AlgorithmConfigProviderUtil struct {
	common.ProviderUtil[AlgorithmConfigProvider]
}

type CongestionConfig struct {
	Id                  int64 `json:"id,omitempty"`
	AlgorithmEnabled    bool  `json:"algorithm_enabled"`
	EarlyWarningEnabled bool  `json:"early_warning_enabled"`
	MaxPendingSec       int   `json:"max_pending_sec"`
}

type CenterStopConfig struct {
	Id                   int64 `json:"id,omitempty"`
	AlgorithmEnabled     bool  `json:"algorithm_enabled"`
	EarlyWarningEnabled  bool  `json:"early_warning_enabled"`
	MaxStopSec           int   `json:"max_stop_sec"`
	AllowDoubleFlash     bool  `json:"allow_double_flash"`
	AllowWarningTriangle bool  `json:"allow_warning_triangle"`
}

type RetrogradeConfig struct {
	Id                  int64        `json:"id,omitempty"`
	AlgorithmEnabled    bool         `json:"algorithm_enabled"`
	EarlyWarningEnabled bool         `json:"early_warning_enabled"`
	ThresholdSec        int          `json:"threshold_sec"`
	MarkPath            *GeomPolygon `json:"mark_path"`
}

type IntrusionConfig struct {
	Id                  int64    `json:"id,omitempty"`
	AlgorithmEnabled    bool     `json:"algorithm_enabled"`
	EarlyWarningEnabled bool     `json:"early_warning_enabled"`
	ObjTypes            []string `json:"obj_types"`
	ResidenceSec        int      `json:"residence_sec"`
}

type AlgorithmConfigProvider interface {
	ExistsByArIdAndVIdSafely(arid int64, vid int64) bool
	GetByAlgorithmRegionIdSafely(arId int64) interface{}
	ConfigToModule(cfg interface{}) interface{}
	Save(ar *Region, ac interface{}) error
	GetConfigForZone(zone *algorithm.TrackZone, ar *Region) error
	DeleteByAlgorithmRegionId(id int64) error
}

func registerAlgorithmProvider(tag string, provider AlgorithmConfigProvider) {
	acpUtil.RegisterProvider(tag, &provider)
}

type AlgorithmModule struct {
	Id                  int64 `json:"id" xorm:"pk autoincr bigint"`
	VideoId             int64 `json:"video_id" xorm:"index not null bigint"`
	AlgorithmRegionId   int64 `json:"ar_id" xorm:"index not null bigint"`
	AlgorithmEnabled    bool  `json:"algorithm_enabled" xorm:"not null default 0"`
	EarlyWarningEnabled bool  `json:"early_warning_enabled" xorm:"not null default 0"`
}
