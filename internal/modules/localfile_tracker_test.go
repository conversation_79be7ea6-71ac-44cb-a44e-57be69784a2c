package modules

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"sync"
	"testing"

	"stvpp/internal/algorithm"
	"stvpp/internal/global"
	"stvpp/internal/mq"

	"github.com/iWay7/go-common/logger"
	"github.com/iWay7/go-common/misc"
)

// LocalVideoConfig 对应于 local_video_config.json 文件中的单个视频对象
type LocalVideoConfig struct {
	CID           string         `json:"cid"`
	PlatIP        string         `json:"plat_ip"`
	PlatPort      int            `json:"plat_port"`
	LocalFilePath string         `json:"local_file_path"`
	ImageSize     []int          `json:"imagesize"`
	Zones         []mq.TrackZone `json:"zones"`
}

var trackerManager *algorithm.TrackerManager

func TestTracker(t *testing.T) {
	mq.Init(mq.Config{
		Addr: "nats://*************:4222",
	})
	algorithm.Init(mq.Nc)

	trackerManager = algorithm.GetTrackerManager()

	// 注册一个事件接收器，用于处理新节点上线事件
	misc.RegisterEventReceiver(func(event string, data interface{}) {
		if event == algorithm.EventNodeAdded {
			if nodeID, ok := data.(string); ok {
				logger.If("检测到新节点 %s，正在从本地文件重新运行追踪器注册。", nodeID)
				runLocalFileTrackerRegistration()
			}
		}
	})
	// select {} 使测试保持运行状态，以接收事件
	select {}
}

func runLocalFileTrackerRegistration() {
	// 1. 读取并解析本地配置文件
	root, err := findProjectRoot()
	if err != nil {
		logger.Ef("查找项目根目录失败: %v", err)
		return
	}
	configPath := filepath.Join(root, global.ConfigDirectory, "local_video_config.json")
	file, err := os.ReadFile(configPath)
	if err != nil {
		logger.Ef("读取本地视频配置文件失败: %v", err)
		return
	}

	var localVideos []LocalVideoConfig
	if err := json.Unmarshal(file, &localVideos); err != nil {
		logger.Ef("解析本地视频配置文件失败: %v", err)
		return
	}

	// 2. 重置现有追踪器
	if err := trackerManager.Reset(); err != nil {
		logger.Ef("重置追踪器失败: %v", err)
		return
	}

	// 3. 为每个视频并行启动处理 goroutine
	var wg sync.WaitGroup
	for _, video := range localVideos {
		wg.Add(1)
		go func(v LocalVideoConfig) {
			defer wg.Done()
			processLocalVideo(v)
		}(video)
	}
	wg.Wait() // 等待所有视频处理完成
	logger.If("所有本地视频追踪器注册流程完成。")
}

func processLocalVideo(video LocalVideoConfig) {
	// 3.1 添加追踪器
	logger.If("正在为 CID %s 添加追踪器", video.CID)
	addMsg := mq.MsgTrackAdd{
		CID:           video.CID,
		PlatIp:        video.PlatIP,
		PlatPort:      strconv.Itoa(video.PlatPort),
		LocalFilePath: video.LocalFilePath,
	}
	if err := trackerManager.AddTracker(addMsg); err != nil {
		logger.Ef("为 CID %s 添加追踪器失败: %v", video.CID, err)
		return
	}

	// 3.2 配置追踪器
	if len(video.Zones) > 0 {
		cfgMsg := mq.MsgTrackCfg{
			CID:       video.CID,
			ImageSize: video.ImageSize,
			Zones:     video.Zones,
		}
		logger.If("正在为 CID %s 配置追踪器", video.CID)
		if err := trackerManager.ConfigureTracker(cfgMsg); err != nil {
			logger.Ef("为 CID %s 配置追踪器失败: %v", video.CID, err)
			return
		}
	} else {
		logger.If("CID %s 未找到有效的本地算法配置，仅添加追踪器。", video.CID)
	}

	// 3.3 启动追踪器
	logger.If("正在为 CID %s 启动追踪器", video.CID)
	if err := trackerManager.StartTracker(video.CID); err != nil {
		logger.Ef("为 CID %s 启动追踪器失败: %v", video.CID, err)
		return
	}

	logger.If("成功为 CID %s 从本地文件配置并启动追踪器", video.CID)
}

func findProjectRoot() (string, error) {
	// 获取当前工作目录
	dir, err := os.Getwd()
	if err != nil {
		return "", fmt.Errorf("获取当前目录失败: %v", err)
	}

	// 向上查找，直到找到 go.mod 文件
	for {
		if _, err := os.Stat(filepath.Join(dir, "go.mod")); err == nil {
			return dir, nil
		}
		// 移动到上层目录
		parent := filepath.Dir(dir)
		if parent == dir {
			// 到达文件系统根目录仍未找到 go.mod
			return "", fmt.Errorf("未找到 go.mod 文件")
		}
		dir = parent
	}
}
