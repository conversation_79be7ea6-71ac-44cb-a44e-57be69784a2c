package modules

import (
	"fmt"
	"stvpp/internal/algorithm"

	"github.com/iWay7/go-common/db"
	"github.com/iWay7/go-common/logger"
)

type IntrusionDao struct {
	db.BaseDao
}

type IntrusionEarlyWarningDao struct {
	db.BaseDao
}

func initIntrusionDao() {
	tag := "intrusion"
	intrusionDao := new(IntrusionDao)
	intrusionDao.initTables()
	registerAlgorithmProvider(tag, intrusionDao)

	intrusionEarlyWarningDao := new(IntrusionEarlyWarningDao)
	intrusionEarlyWarningDao.initTables()
	registerEarlyWarningConfigProvider(tag, intrusionEarlyWarningDao)
}

type Intrusion struct {
	AlgorithmModule `xorm:"extends"`
	ObjTypes        []string `json:"obj_types" xorm:"not null json comment('入侵检测对象类型，0未知，1行人，2摩托车，3三轮车，4汽车')"`
	ResidenceSec    int      `json:"residence_sec" xorm:"not null int"`
	CreatedAt       int64    `json:"created_at" xorm:"index created"`
	UpdatedAt       int64    `json:"updated_at" xorm:"index updated"`
}

type IntrusionEarlyWarning struct {
	EarlyWarningModule `xorm:"extends"`
	MaxResidenceSec    int           `json:"max_residence_sec" xorm:"not null int"`
	ForceWarning       *ForceWarning `json:"force_warning" xorm:"not null json"`
	CreatedAt          int64         `json:"created_at" xorm:"index created"`
	UpdatedAt          int64         `json:"updated_at" xorm:"index updated"`
}

func (intrusion *Intrusion) TableName() string {
	return "algo_intrusion"
}

func (intrusion *IntrusionEarlyWarning) TableName() string {
	return "ew_intrusion"
}

func (intrusionDao *IntrusionDao) ExistsByArIdAndVIdSafely(arid int64, vid int64) bool {
	count, err := db.NewSession().Where("algorithm_region_id = ? and video_id = ?", arid, vid).Count(new(Intrusion))
	if err != nil {
		return false
	}
	return count > 0
}

func (intrusionDao *IntrusionDao) GetByAlgorithmRegionIdSafely(arId int64) interface{} {
	var result Intrusion
	_, err := db.NewSession().Where("algorithm_region_id = ?", arId).Get(&result)
	if err != nil {
		logger.Ef("intrusionDao.GetByAlgorithmRegionIdSafely: %v", err)
		return nil
	}
	if result.Id == 0 {
		return nil
	}
	return &IntrusionConfig{
		Id:                  result.Id,
		AlgorithmEnabled:    result.AlgorithmEnabled,
		EarlyWarningEnabled: result.EarlyWarningEnabled,
		ObjTypes:            result.ObjTypes,
		ResidenceSec:        result.ResidenceSec,
	}
}

func (intrusionDao *IntrusionDao) ConfigToModule(cfg interface{}) interface{} {
	if cfg == nil {
		return nil
	}
	c, ok := cfg.(*IntrusionConfig)
	if !ok {
		return nil
	}
	return &Intrusion{
		AlgorithmModule: AlgorithmModule{
			Id:                  c.Id,
			AlgorithmEnabled:    c.AlgorithmEnabled,
			EarlyWarningEnabled: c.EarlyWarningEnabled,
		},
		ObjTypes:     c.ObjTypes,
		ResidenceSec: c.ResidenceSec,
	}
}

func (intrusionDao *IntrusionDao) Save(ar *Region, i interface{}) error {
	in, ok := i.(*Intrusion)
	if !ok {
		return fmt.Errorf("intrusionDao.Save: invalid type %T", i)
	}
	in.VideoId = ar.VideoId
	in.AlgorithmRegionId = ar.Id
	if in.Id != 0 {
		_, err := db.GetWithTransactionOrNewSession().ID(in.Id).AllCols().Update(in)
		if err != nil {
			return fmt.Errorf("db.NewSession().ID(in.AlgorithmConfigId).AllCols().Update(in): %v", err)
		}
		return nil
	}
	_, err := db.GetWithTransactionOrNewSession().Insert(in)
	if err != nil {
		return fmt.Errorf("db.NewSession().Insert(in): %v", err)
	}
	return nil
}

func (intrusionDao *IntrusionDao) GetConfigForZone(zone *algorithm.TrackZone, ar *Region) error {
	cfg := intrusionDao.GetByAlgorithmRegionIdSafely(ar.Id)
	if cfg != nil {
		type Cfg struct {
			ObjTypes     []string `json:"obj_types"`
			ResidenceSec int      `json:"residence_sec"`
		}
		c := cfg.(*IntrusionConfig)
		zone.Intrusion = &Cfg{
			ObjTypes:     c.ObjTypes,
			ResidenceSec: c.ResidenceSec,
		}
	}
	return nil
}

func (intrusionDao *IntrusionDao) DeleteByAlgorithmRegionId(id int64) error {
	_, err := db.GetWithTransactionOrNewSession().Where("algorithm_region_id = ?", id).Delete(new(Intrusion))
	if err != nil {
		return fmt.Errorf("db.NewSession().Where(\"algorithm_region_id = ?\", id).Delete(new(Intrusion)): %v", err)
	}
	return nil
}

func (intrusionDao *IntrusionDao) initTables() {
	err := db.InitTable(new(Intrusion), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(Intrusion), nil): %v", err)
		panic(err)
	}
}

func (intrusionEarlyWarningDao *IntrusionEarlyWarningDao) Save(taskId int64, i interface{}) error {
	ew, ok := i.(*IntrusionEarlyWarning)
	if !ok {
		return fmt.Errorf("intrusionEarlyWarningDao.Save: invalid type %T", i)
	}
	ew.TaskId = taskId
	if ew.Id != 0 {
		_, err := db.GetWithTransactionOrNewSession().ID(ew.Id).AllCols().Update(ew)
		if err != nil {
			return fmt.Errorf("db.NewSession().ID(ew.Id).AllCols().Update(ew): %v", err)
		}
		return nil
	}
	_, err := db.GetWithTransactionOrNewSession().Insert(ew)
	if err != nil {
		return fmt.Errorf("db.NewSession().Insert(ew): %v", err)
	}
	return nil
}

func (intrusionEarlyWarningDao *IntrusionEarlyWarningDao) DeleteByTaskId(taskId int64) error {
	_, err := db.GetWithTransactionOrNewSession().Where("task_id = ?", taskId).Delete(new(IntrusionEarlyWarning))
	if err != nil {
		return fmt.Errorf("db.NewSession().Where(\"task_id = ?\", taskId).Delete(new(IntrusionEarlyWarning)): %v", err)
	}
	return nil
}

func (intrusionEarlyWarningDao *IntrusionEarlyWarningDao) initTables() {
	err := db.InitTable(new(IntrusionEarlyWarning), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(IntrusionEarlyWarning), nil): %v", err)
		panic(err)
	}
}
