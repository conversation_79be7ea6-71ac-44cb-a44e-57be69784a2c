package modules

import (
	"fmt"

	"github.com/iWay7/go-common/db"
)

type PlatformDao struct {
	db.BaseDao
}

var platformDao *PlatformDao

func initPlatformDao() {
	platformDao = new(PlatformDao)
	platformDao.initTables()
}

func GetPlatformDao() *PlatformDao {
	return platformDao
}

type Platform struct {
	Id           int64  `json:"id" xorm:"pk autoincr bigint"`
	Name         string `json:"name" xorm:"not null varchar(255)"`
	Code         string `json:"code" xorm:"not null char(20)"`
	Manufacturer string `json:"manufacturer" xorm:"not null char(12)"`
	Ip           string `json:"ip" xorm:"not null varchar(20)"`
	Port         int    `json:"port" xorm:"not null int"`
	Username     string `json:"username" xorm:"not null varchar(255)"`
	Password     string `json:"password" xorm:"not null varchar(255)"`
	Description  string `json:"description" xorm:"varchar(255)"`
	CreatedAt    int64  `json:"created_at" xorm:"index created"`
	UpdatedAt    int64  `json:"updated_at" xorm:"index updated"`
	Ptxh         int64  `json:"-" xorm:"unique not null int"`
}

func (platform *Platform) TableName() string {
	return "base_platform"
}

func (platformDao *PlatformDao) GetById(id int64) (*Platform, error) {
	var result Platform
	got, err := db.NewSession().ID(id).Get(&result)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().ID(id).Get(&result): %v", err)
	}
	if !got {
		return nil, nil
	}
	return &result, nil
}

func (platformDao *PlatformDao) GetIdByPtxh(ptxh int64) (int64, error) {
	var result Platform
	_, err := db.NewSession().Where("ptxh = ?", ptxh).Cols("id").Get(&result)
	if err != nil {
		return 0, fmt.Errorf("db.NewSession().Where(\"ptxh = ?\", ptxh).Select(\"id\").Get(&result): %v", err)
	}
	return result.Id, nil
}

func (platformDao *PlatformDao) GetNameByIdSafely(id int64) string {
	var result Platform
	_, err := db.NewSession().ID(id).Select("name").Get(&result)
	if err != nil {
		return ""
	}
	return result.Name
}

func (platformDao *PlatformDao) ListAll() ([]*Platform, error) {
	var platforms []*Platform
	err := db.NewSession().Find(&platforms)
	if err != nil {
		return nil, fmt.Errorf("db.NewSession().Find(&platform): %v", err)
	}
	return platforms, nil
}

func (platformDao *PlatformDao) Add(platform *Platform) error {
	_, err := db.NewSession().Insert(platform)
	if err != nil {
		return fmt.Errorf("db.NewSession().Insert(platform): %v", err)
	}
	return nil
}

func (platformDao *PlatformDao) Update(platform *Platform) error {
	_, err := db.NewSession().ID(platform.Id).AllCols().Update(platform)
	if err != nil {
		return fmt.Errorf("db.NewSession().ID(platform.AlgorithmConfigId).AllCols().Update(platform): %v", err)
	}
	return nil
}

func (platformDao *PlatformDao) Save(platform *Platform) error {
	id, err := platformDao.GetIdByPtxh(platform.Ptxh)
	if err != nil {
		return err
	}
	if id != 0 {
		platform.Id = id
		return platformDao.Update(platform)
	} else {
		return platformDao.Add(platform)
	}
}

func (platformDao *PlatformDao) Delete(id int64) error {
	_, err := db.NewSession().ID(id).Delete(new(Platform))
	if err != nil {
		return fmt.Errorf("db.NewSession().ID(id).Delete(new(Platform)): %v", err)
	}
	return nil
}

func (platformDao *PlatformDao) initTables() {
	err := db.InitTable(new(Platform), nil)
	if err != nil {
		err = fmt.Errorf("db.InitTable(new(Platform), nil): %v", err)
		panic(err)
	}
}
