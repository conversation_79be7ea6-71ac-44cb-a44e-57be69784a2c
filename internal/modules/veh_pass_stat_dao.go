package modules

import (
	"fmt"

	"github.com/iWay7/go-common/db"
)

type VehPassStatDao struct {
	db.BaseDao
}

var vehPassStatDao *VehPassStatDao

func initVehPassStatDao() {
	vehPassStatDao = new(VehPassStatDao)
	vehPassStatDao.initTables()
}

func GetVehPassStatDao() *VehPassStatDao {
	return vehPassStatDao
}

type VehPassStat struct {
	Id        int64  `json:"id" xorm:"pk autoincr bigint"`
	CID       string `json:"cid" xorm:"c_id index not null varchar(255)"`
	RID       string `json:"rid" xorm:"r_id not null varchar(255)"`
	StartTime int64  `json:"start_time" xorm:"index(time) not null int"`
	EndTime   int64  `json:"end_time" xorm:"index(time) not null int"`
	Stage1    int64  `json:"stage1" xorm:"not null int"`
	Stage2    int64  `json:"stage2" xorm:"not null int"`
	Stage3    int64  `json:"stage3" xorm:"not null int"`
	Stage4    int64  `json:"stage4" xorm:"not null int"`
	Stage5    int64  `json:"stage5" xorm:"not null int"`
	Stage6    int64  `json:"stage6" xorm:"not null int"`
	Stage7    int64  `json:"stage7" xorm:"not null int"`
	Stage8    int64  `json:"stage8" xorm:"not null int"`
	Stage9    int64  `json:"stage9" xorm:"not null int"`
	Stage10   int64  `json:"stage10" xorm:"not null int"`
	VehTime   int64  `json:"veh_time" xorm:"not null int"`
	VehCount  int    `json:"veh_count" xorm:"not null int"`
	CreatedAt int64  `json:"created_at" xorm:"index created"`
}

func (stat *VehPassStat) TableName() string {
	return "stat_veh_pass"
}

func (dao *VehPassStatDao) Add(stat *VehPassStat) error {
	_, err := db.NewSession().Insert(stat)
	return err
}

func (dao *VehPassStatDao) initTables() {
	err := db.InitTable(new(VehPassStat), nil)
	if err != nil {
		panic(fmt.Errorf("db.InitTable(new(VehPassStat), nil): %v", err))
	}
}
