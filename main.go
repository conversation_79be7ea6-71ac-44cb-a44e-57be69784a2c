package main

import (
	"net/http"
	"stvpp/global"
	"stvpp/internal/algorithm"
	"stvpp/internal/api"
	"stvpp/internal/gateway"
	"stvpp/internal/modules"
	"stvpp/internal/mq"
	"stvpp/internal/warning"
	"time"

	"github.com/iWay7/go-common/cache"
	"github.com/iWay7/go-common/db"
	"github.com/iWay7/go-common/geo"
	"github.com/iWay7/go-common/iam"
	"github.com/iWay7/go-common/kv"
	"github.com/iWay7/go-common/logger"
	"github.com/iWay7/go-common/misc"
	"github.com/iWay7/go-common/router"
	"github.com/iWay7/go-common/server"
	"github.com/iWay7/go-common/sysmon"
	"xorm.io/xorm/log"
)

func main() {
	misc.Init()
	global.Init()
	appConfig := global.GetAppConfig()
	logger.Init(logger.Config{
		Directory:      global.LogDirectory,
		FileName:       global.LogFile,
		MaxRecentLogs:  2048,
		LogZipKeepDays: 30,
	})
	logger.If("app started, version name: %v, version date: %v, version time: %v",
		global.AppVersionName,
		global.AppVersionDate,
		global.AppVersionTime)
	server.Init(server.Config{
		AppName:     "智慧交通视频巡逻平台",
		HttpEnabled: !appConfig.DisableHttp,
		HttpAddress: appConfig.HttpAddress,
		HttpServerMod: func(server *http.Server) {
			if appConfig.HttpServerMod {
				server.ReadHeaderTimeout = time.Second * 20
				server.ReadTimeout = time.Second * 8
				server.WriteTimeout = time.Second * 8
			}
		},
		HttpsEnabled: !appConfig.DisableHttps,
		HttpsAddress: appConfig.HttpsAddress,
		HttpsServerMod: func(server *http.Server) {
			if appConfig.HttpsServerMod {
				server.ReadHeaderTimeout = time.Second * 20
				server.ReadTimeout = time.Second * 8
				server.WriteTimeout = time.Second * 8
			}
		},
		HttpsCertFile: appConfig.HttpsCertFile,
		HttpsKeyFile:  appConfig.HttpsKeyFile,
		IndexUrl:      "/static/index.html",
	})
	cache.Init(cache.Config{
		RedisAddr:     appConfig.RedisAddr,
		RedisPassword: appConfig.RedisPassword,
		RedisDB:       appConfig.RedisDB,
	})
	db.Init(db.Config{
		MySQLConnString:  appConfig.MySQLConnString,
		MySQLConnMaxIdle: appConfig.MySQLConnMaxIdle,
		MySQLConnMaxOpen: appConfig.MySQLConnMaxOpen,
		LockDirectory:    global.LockDirectory,
		LogLevel:         log.LOG_WARNING,
	})
	kv.Init()
	// storage.Init(storage.Config{
	// 	LogFile: global.StorageLogFile,
	// })
	// storage.SetMediums([]storage.Medium{
	// 	storage.NewFSMedium(storage.FSMediumConfig{
	// 		Name:     "fs",
	// 		Priority: 1,
	// 		FsRoot:   global.FileStoreDirectory,
	// 	}),
	// })
	router.Init(router.Config{
		URIPrefix: "/service",
	})
	geo.Init(geo.Config{
		MapDirectory: global.MapAssetDirectory,
		ReadCity: func() (string, string) {
			organizationDao := iam.GetOrganizationDao()
			root, err := organizationDao.GetRoot()
			if err != nil {
				return root.Province, root.City
			}
			return "", ""
		},
	})
	iam.Init(iam.Config{
		AssetDirectory:     global.AssetDirectory,
		DownloadDirectory:  global.DownloadDirectory,
		UploadDirectory:    global.UploadDirectory,
		DefaultProductName: "智慧交通视频巡逻平台",
		ReadVersionInfo: func() iam.VersionInfo {
			return iam.VersionInfo{
				AppVersionName: global.AppVersionName,
				AppVersionCode: global.AppVersionCode,
				AppVersionDate: global.AppVersionDate,
				AppVersionTime: global.AppVersionTime,
			}
		},
		ReadUriFunctionMap: func(m map[string]string) map[string]string {
			m["/config/createMachineCode"] = "/SystemManage/SystemInfo"
			m["/config/validateAuthCode"] = "/SystemManage/SystemInfo"
			return m
		},
		ReadBusinessFunctions: func() []iam.Function {
			return api.BusinessFunctions
		},
	})
	server.HandleDir("/static/", global.HtmlDirectory)
	sysmon.Init(sysmon.Config{
		MonitorDiskSpace: sysmon.MonitorDiskSpaceConfig{
			Enabled:  true,
			Interval: time.Minute * 5,
			Percent:  10,
		},
	})

	modules.Init()
	api.Init()

	gateway.Init(gateway.Config{
		Host: appConfig.GatewayServiceHost,
		Port: appConfig.GatewayServicePort,
	})
	mq.Init(mq.Config{
		Addr: appConfig.NatsAddr,
	})

	algorithm.Init(mq.Nc)
	modules.InitTaskManager()

	warning.Init(mq.Nc)
	server.Run()
}
