module stvpp

go 1.23.0

toolchain go1.23.2

require (
	github.com/chai/create-api v0.0.0
	github.com/iWay7/go-common/cache v0.0.0
	github.com/iWay7/go-common/db v0.0.0
	github.com/iWay7/go-common/geo v0.0.0
	github.com/iWay7/go-common/iam v0.0.0
	github.com/iWay7/go-common/kv v0.0.0
	github.com/iWay7/go-common/logger v0.0.0
	github.com/iWay7/go-common/misc v0.0.0
	github.com/iWay7/go-common/router v0.0.0
	github.com/iWay7/go-common/server v0.0.0
	github.com/iWay7/go-common/sysmon v0.0.0
	github.com/nats-io/nats.go v1.43.0
	github.com/sirupsen/logrus v1.9.3
	github.com/twpayne/go-geom v1.6.1
	xorm.io/xorm v1.3.10
)

require (
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/go-redis/redis/v8 v8.11.5 // indirect
	github.com/go-resty/resty/v2 v2.16.5 // indirect
	github.com/go-sql-driver/mysql v1.9.3 // indirect
	github.com/goccy/go-json v0.10.5 // indirect
	github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0 // indirect
	github.com/golang/snappy v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mojocn/base64Captcha v1.3.8 // indirect
	github.com/nats-io/nkeys v0.4.11 // indirect
	github.com/nats-io/nuid v1.0.1 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.4 // indirect
	github.com/syndtr/goleveldb v1.0.0 // indirect
	github.com/tiendc/go-deepcopy v1.6.1 // indirect
	github.com/xuri/efp v0.0.1 // indirect
	github.com/xuri/excelize/v2 v2.9.1 // indirect
	github.com/xuri/nfp v0.0.1 // indirect
	golang.org/x/crypto v0.41.0 // indirect
	golang.org/x/image v0.30.0 // indirect
	golang.org/x/net v0.43.0 // indirect
	golang.org/x/sys v0.35.0 // indirect
	golang.org/x/text v0.28.0 // indirect
	xorm.io/builder v0.3.13 // indirect
)

replace (
	github.com/chai/create-api v0.0.0 => ../create-api
	github.com/iWay7/go-common/algorithm v0.0.0 => ../go-common/algorithm
	github.com/iWay7/go-common/cache v0.0.0 => ../go-common/cache
	github.com/iWay7/go-common/common v0.0.0 => ../go-common/common
	github.com/iWay7/go-common/db v0.0.0 => ../go-common/db
	github.com/iWay7/go-common/geo v0.0.0 => ../go-common/geo
	github.com/iWay7/go-common/iam v0.0.0 => ../go-common/iam
	github.com/iWay7/go-common/kv v0.0.0 => ../go-common/kv
	github.com/iWay7/go-common/logger v0.0.0 => ../go-common/logger
	github.com/iWay7/go-common/misc v0.0.0 => ../go-common/misc
	github.com/iWay7/go-common/router v0.0.0 => ../go-common/router
	github.com/iWay7/go-common/server v0.0.0 => ../go-common/server
	github.com/iWay7/go-common/standard v0.0.0 => ../go-common/standard
	github.com/iWay7/go-common/storage v0.0.0 => ../go-common/storage
	github.com/iWay7/go-common/svcconn v0.0.0 => ../go-common/svcconn
	github.com/iWay7/go-common/sysmon v0.0.0 => ../go-common/sysmon
)
