package global

import (
	"encoding/json"
	"fmt"
	"net"
	"os"

	"github.com/iWay7/go-common/misc"
)

type AppConfig struct {
	MySQLConnString    string `json:"mysql_conn_string"`
	MySQLConnMaxIdle   int    `json:"mysql_conn_max_idle"`
	MySQLConnMaxOpen   int    `json:"mysql_conn_max_open"`
	RedisAddr          string `json:"redis_addr"`
	RedisPassword      string `json:"redis_password"`
	RedisDB            int    `json:"redis_db"`
	TFAIServiceHost    string `json:"tfai_service_host"`
	TFAIServicePort    int    `json:"tfai_service_port"`
	TFAIServiceAppId   string `json:"tfai_service_app_id"`
	DisableHttp        bool   `json:"disable_http"`
	HttpAddress        string `json:"http_address"`
	HttpServerMod      bool   `json:"http_server_mod"`
	DisableHttps       bool   `json:"disable_https"`
	HttpsAddress       string `json:"https_address"`
	HttpsCertFile      string `json:"https_cert_file"`
	HttpsKeyFile       string `json:"https_key_file"`
	HttpsServerMod     bool   `json:"https_server_mod"`
	LocalHost          string `json:"local_host"`
	GatewayServiceHost string `json:"gateway_service_host"`
	GatewayServicePort int    `json:"gateway_service_port"`
	NatsAddr           string `json:"nats_addr"`
}

var appConfig = AppConfig{}

func initAppConfig() {
	configData, err := os.ReadFile(AppConfigFile)
	if err == nil {
		tempAppConfig := AppConfig{}
		err = json.Unmarshal(configData, &tempAppConfig)
		if err == nil {
			appConfig = tempAppConfig
		} else {
			panic(fmt.Errorf("load app config failed: %v", err))
		}
	}
	if appConfig.MySQLConnString == "" {
		appConfig.MySQLConnString = "deque:zxcv0987@tcp(127.0.0.1:3306)/frag_store?charset=utf8mb4&collation=utf8mb4_general_ci&loc=Asia%2FShanghai"
	}
	if appConfig.MySQLConnMaxIdle == 0 {
		appConfig.MySQLConnMaxIdle = 64
	}
	if appConfig.MySQLConnMaxOpen == 0 {
		appConfig.MySQLConnMaxOpen = 64
	}
	if appConfig.RedisAddr == "" {
		appConfig.RedisAddr = "127.0.0.1:6379"
	}
	if appConfig.RedisDB == 0 {
		appConfig.RedisDB = 3
	}
	if appConfig.RedisPassword == "" {
		appConfig.RedisPassword = "Passw0rd@ylzl"
	}
	if appConfig.TFAIServiceHost == "" {
		appConfig.TFAIServiceHost = "127.0.0.1"
	}
	if appConfig.TFAIServicePort == 0 {
		appConfig.TFAIServicePort = 9527
	}
	if appConfig.TFAIServiceAppId == "" {
		appConfig.TFAIServiceAppId = "frag-store"
	}
	if appConfig.HttpAddress == "" {
		appConfig.HttpAddress = "0.0.0.0:8089"
	}
	if appConfig.HttpsAddress == "" {
		appConfig.HttpsAddress = "0.0.0.0:18089"
	}
	if appConfig.HttpsCertFile == "" {
		appConfig.HttpsCertFile = "conf/https.crt"
	}
	if appConfig.HttpsKeyFile == "" {
		appConfig.HttpsKeyFile = "conf/https.key"
	}
	if appConfig.LocalHost == "" {
		appConfig.LocalHost = "auto"
	}
	_ = misc.WriteJsonFile(AppConfigFile, appConfig)
}

func GetAppConfig() AppConfig {
	if appConfig.LocalHost == "auto" {
		addrs, err := net.InterfaceAddrs()
		if err == nil {
			for _, addr := range addrs {
				if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
					ipV4 := ipNet.IP.To4()
					if ipV4 != nil {
						appConfig.LocalHost = ipV4.String()
						break
					}
				}
			}
		}
	}
	return appConfig
}
