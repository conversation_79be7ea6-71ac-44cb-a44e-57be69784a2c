package global

import (
	"os"
	"runtime"
)

const PathSeparator = string(os.PathSeparator)

const ConfigDirectory = "conf"
const AppConfigFile = ConfigDirectory + PathSeparator + "app_config.json"

const HtmlDirectory = "html"

const LogDirectory = "logs"
const TaskLogDirectory = LogDirectory + PathSeparator + "task"
const LogFile = "today.log"

const AssetDirectory = "asset"
const AppPackagesAssetDirectory = AssetDirectory + PathSeparator + "app_packages"
const MapAssetDirectory = AssetDirectory + PathSeparator + "map"
const HelperAssetDirectory = AssetDirectory + PathSeparator + "helper"

const FFmpegFilePath = AssetDirectory + PathSeparator + "ffmpeg_" + runtime.GOARCH + PathSeparator + "ffmpeg"
const FFprobeFilePath = AssetDirectory + PathSeparator + "ffmpeg_" + runtime.GOARCH + PathSeparator + "ffprobe"

const DataDirectory = "data"
const TempDirectory = DataDirectory + PathSeparator + "temp"
const LockDirectory = DataDirectory + PathSeparator + "lock"
const MiscDirectory = DataDirectory + PathSeparator + "common"
const DownloadDirectory = DataDirectory + PathSeparator + "download"
const UploadDirectory = DataDirectory + PathSeparator + "upload"
const FileStoreDirectory = DataDirectory + PathSeparator + "file_store"
const TaskDirectory = DataDirectory + PathSeparator + "task"
const RecordDirectory = DataDirectory + PathSeparator + "record"
const ShrinkDirectory = DataDirectory + PathSeparator + "shrink"
const EnhancerDirectory = DataDirectory + PathSeparator + "enhancer"
const StorageLogFile = MiscDirectory + "/storage.log"
const SyncLogFile = MiscDirectory + "/sync.log"
const TrackerLogFile = MiscDirectory + "/tracker.log"
const TaskLogFile = MiscDirectory + "/task.log"

func initPaths() {
	_ = os.Mkdir(ConfigDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(HtmlDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(LogDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(TaskLogDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(AssetDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(AppPackagesAssetDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(MapAssetDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(DataDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(TempDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(LockDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(MiscDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(DownloadDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(UploadDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(FileStoreDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(TaskDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(RecordDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(ShrinkDirectory, os.ModeDir|os.ModePerm)
	_ = os.Mkdir(EnhancerDirectory, os.ModeDir|os.ModePerm)
}
