# stvpp 项目概览

## 1. 项目目标

`stvpp` (根据代码推断，全称为 **S**mart **T**raffic **V**ideo **P**atrol **P**latform，即 **智慧交通视频巡逻平台**)
是一个为智能交通管理设计的综合性后端系统。

根据其代码结构，核心功能包括：

- **视频管理**: 管理来自不同平台和设备的视频流。
- **交通事件分析**: 检测并管理交通事件，例如 **拥堵**、**逆行** 和 **中心停车**。
- **实时追踪**: 利用复杂的算法模块，在不同的视频节点间进行实时的目标（车辆）追踪。
- **数据同步**: 提供与外部或第三方系统进行数据同步的机制。
- **网关集成**: 与一个专用的视频接入网关通信，以管理设备和视频流。
- **API服务**: 为前端应用或其他服务提供一套 RESTful API，用于管理和查询交通数据。
- **预警**: 接收来自算法模块的预警消息，并进行相应的处理。

## 2. 核心技术栈

- **编程语言**: Go
- **编程环境**: GoLand、Windows11、cmd
    - 直行命令时，使用cmd命令，不要使用linux下的命令。如删除文件时应该使用cmd中的`del`而不是linux下的`rm`。
- **Web框架**: 基于 `net/http` 的自定义路由，由内部库 `go-common/router` 提供。
- **数据库**: MySQL，通过 ORM 库 `xorm.io/xorm` 进行访问。
- **消息队列**: NATS (`nats.io`)，用于模块间的异步通信，尤其是在 `algorithm` 核心算法模块中。
- **缓存**: Redis，通过 `go-redis` 库。
- **配置**: 通过中心的 `conf/app_config.json` 文件进行管理。
- **日志**: 通过内部库 `go-common/logger` 进行处理。该库提供了一组包级别的函数用于不同级别的日志记录。主要函数包括：
    - `logger.Ef(format string, args ...interface{})`: 记录错误（Error）级别的日志。
    - `logger.If(format string, args ...interface{})`: 记录信息（Info）级别的日志。
    - `logger.Wf(format string, args ...interface{})`: 记录警告（Warning）级别的日志。
    - `logger.Df(format string, args ...interface{})`: 记录调试（Debug）级别的日志。
      所有函数都接受一个格式化字符串和相应的参数。
- **关键依赖库**:
    - `go-common/*`: 一套内部共享库，用于处理数据库、日志、服务启动等通用任务。
    - `go-geom`: 用于处理地理/几何数据和空间计算（例如，定义区域）。
    - `resty`: 用于发起 HTTP 客户端请求，很可能在 `gateway` 模块中使用。

## 3. 架构概览

项目遵循模块化、分层的架构，实现了良好的关注点分离。

### 应用启动流程

1. **`main.go`**: 应用程序的入口点。
2. **初始化**: 顺序初始化核心组件：
    - `global`: 加载配置 (`app_config.json`) 并设置全局常量（路径、版本等）。
    - `logger`: 配置日志系统。
    - `server`: 设置 HTTP/HTTPS 服务器。
    - `cache`: 初始化 Redis 连接。
    - `db`: 使用 XORM 初始化 MySQL 连接池。
    - `router`: 初始化 API 路由引擎。
    - `iam`: 初始化身份与访问管理模块。
    - `modules`: 初始化数据访问层 (DAOs)，并确保数据库表结构就绪。
    - `gateway`: 初始化用于连接视频接入网关服务的客户端。
    - `mq`: 初始化到 NATS 服务器的连接。
    - `algorithm`: 初始化核心的交通分析和追踪引擎，该引擎会订阅和发布 NATS 消息。
    - `warning`: 初始化预警模块，订阅 NATS 消息并处理预警事件。
3. **运行**: 启动 HTTP/HTTPS 服务器，开始监听外部请求。

### 关键模块及其职责

- **`/main.go`**: 应用入口和总初始化器。
- **`/algorithm`**: **核心逻辑层**。这是应用的大脑。它包含了视频追踪 (`video_tracker.go`)、节点管理 (`node_manager.go`)
  和追踪器管理 (`tracker_manager.go`) 的逻辑。它主要通过 NATS 消息进行异步操作。
- **`/api`**: **API 层**。定义 RESTful API 接口。此层的控制器处理传入的 HTTP 请求，验证输入，并调用其他模块的业务逻辑。
- **`/asset`**: 包含静态资源，如logo、应用程序包和地图数据。
- **`/conf`**: 包含所有静态配置文件，如 `app_config.json` 和 SSL 证书。
- **`/deploy`**: 包含用于构建、打包和部署应用的脚本 (`build.sh`, `package_*.sh`)。
- **`/gateway`**: **网关客户端层**。作为外部“视频接入网关”服务的客户端。它负责与该网关的所有通信，例如发送命令或获取流信息。
- **`/global`**: 处理全局配置和常量，是应用级设置的中心。
- **`/misc`**: 包含各种工具代码，例如IP地址处理和雪花ID生成。
- **`/modules`**: **数据访问层 (DAL)**。包含所有数据库交互的抽象，即数据访问对象 (DAO)。它定义了映射到数据库表的数据模型 (
  struct)，并提供增删改查的方法。
- **`/mq`**: **消息队列层**。一个简单的包装层，用于初始化并提供对 NATS 连接 (`mq.Nc`) 的访问。
- **`/warning`**: **预警模块**。接收（预警端）通过mq发送的消息，基于消息类型对消息做不同的处理。

### 数据流示例 (拥堵检测)

1. 本地程序配置*拥堵*算法参数。
2. 本地程序直行init_tracker后，将本地的拥堵算法配置通过mq想（算法端）发起添加(MQ_MSG_TRACK_ADD)、配置(MQ_MSG_TRACK_CFG)
   、启动(MQ_MSG_TRACK_START)视频追踪任务。
3. （算法端）持续运行，本地`algorithm` 模块中的消费者（例如 `tracker_manager`）监控视频追踪器的运行状态。
4. （预警端）检测到*拥堵*，通过mq向（应用端）发送预警消息。
5. （应用端）warning模块接收到消息后，通过type来处理*拥堵*消息。
6. （应用端）将接收到的拥堵统计存入数据库。它调用 `modules.CongestionDao` 中的一个函数。`CongestionDao` 在 MySQL 数据库的
   `congestion` 表中创建一条新记录。
7. （用户）通过 API 请求拥堵数据。
8. 请求到达 `api` 层的 `CongestionController`。
9. 控制器调用 `modules.CongestionDao` 从数据库中获取数据。
10. 控制器格式化响应并将其发送回用户。

## 4. 如何构建和运行

### 配置

- 所有运行时配置都在 `conf/app_config.json` 中。
- 在运行前，请确保正确设置了 MySQL、Redis 和 NATS 的连接信息。
- 同时，核对 `gateway_service` 的主机和端口。

### 构建

- 项目使用 Go Modules，并包含一个 `vendor` 目录来管理依赖。
- 主要的构建脚本是 `deploy/build.sh`。
- 手动构建步骤:
  ```bash
  # 整理并下载依赖到 vendor 目录
  go mod tidy
  go mod vendor

  # 构建二进制文件
  go build -o stvpp main.go
  ```

### 运行

- 编译后的二进制文件可以直接执行:
  ```bash
  ./stvpp
  ```
- `deploy/package/start.sh` 脚本提供了一种更正式的启动服务的方式，可能用于生产环境。